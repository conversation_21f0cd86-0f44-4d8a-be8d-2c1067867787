<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoPilot AI - 旅行规划系统 V3.0 重构版</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 自定义CSS -->
    <link href="/static/css/travel_planner_v3.css" rel="stylesheet">
    
    <style>
        .analysis-step {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }
        
        .analysis-step.completed {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        
        .analysis-step.running {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        
        .analysis-placeholder {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .control-buttons {
            margin: 20px 0;
        }
        
        .control-buttons button {
            margin-right: 10px;
        }
        
        #vehicleInfoPanel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .voice-controls {
            margin: 10px 0;
        }
        
        .voice-controls button {
            margin-right: 5px;
        }
        
        .progress-text {
            font-weight: bold;
            color: #0d6efd;
            margin: 10px 0;
        }
        
        .error-message {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧分析面板 -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4><i class="bi bi-brain"></i> AI智能分析</h4>
                        <small class="text-muted">透明化展示AI分析思考过程</small>
                    </div>
                    <div class="card-body">
                        <!-- 用户输入区域 -->
                        <div class="mb-3">
                            <label for="userQuery" class="form-label">描述您的旅行需求</label>
                            <textarea class="form-control" id="userQuery" rows="3" 
                                placeholder="例如：我想去北京玩3天，喜欢历史文化景点，需要自驾出行"></textarea>
                        </div>
                        
                        <!-- 车辆信息面板 -->
                        <div id="vehicleInfoPanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6><i class="bi bi-car-front"></i> 车辆信息</h6>
                                <button class="btn btn-sm btn-outline-secondary" id="toggleVehicleInfo">
                                    <i class="bi bi-chevron-up"></i> 收起
                                </button>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="vehicleModel" class="form-label">车型</label>
                                    <select class="form-select" id="vehicleModel">
                                        <option value="">请选择车型</option>
                                        <option value="特斯拉Model 3">特斯拉Model 3</option>
                                        <option value="比亚迪汉">比亚迪汉</option>
                                        <option value="蔚来ES6">蔚来ES6</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="vehicleRange" class="form-label">续航里程(km)</label>
                                    <input type="number" class="form-control" id="vehicleRange" placeholder="500">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <label for="chargingType" class="form-label">充电类型</label>
                                    <select class="form-select" id="chargingType">
                                        <option value="快充">快充</option>
                                        <option value="慢充">慢充</option>
                                        <option value="超充">超充</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="drivingMode" class="form-label">运行模式</label>
                                    <select class="form-select" id="drivingMode">
                                        <option value="interactive">交互式</option>
                                        <option value="automatic">全自动</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 控制按钮 -->
                        <div class="control-buttons">
                            <button class="btn btn-primary" id="startAnalysisBtn">
                                <i class="bi bi-play-circle"></i> 开始分析
                            </button>
                            <button class="btn btn-success" id="startPlanningBtn" style="display: none;">
                                <i class="bi bi-lightning"></i> 立即规划
                            </button>
                            <button class="btn btn-warning" id="pausePlanningBtn" style="display: none;">
                                <i class="bi bi-pause-circle"></i> 暂停
                            </button>
                            <button class="btn btn-danger" id="cancelPlanningBtn" style="display: none;">
                                <i class="bi bi-stop-circle"></i> 取消
                            </button>
                        </div>
                        
                        <!-- 语音控制 -->
                        <div class="voice-controls">
                            <button class="btn btn-sm btn-outline-info" id="ttsToggleAnalysis">
                                <i class="bi bi-volume-up"></i> 语音开启
                            </button>
                            <button class="btn btn-sm btn-outline-warning" id="pauseTTS">
                                <i class="bi bi-pause"></i> 暂停
                            </button>
                            <button class="btn btn-sm btn-outline-danger" id="stopTTS">
                                <i class="bi bi-stop"></i> 停止
                            </button>
                        </div>
                        
                        <!-- 进度显示 -->
                        <div class="progress-text" id="progressText">等待开始分析...</div>
                        
                        <!-- 错误消息 -->
                        <div class="error-message" id="errorMessage" style="display: none;"></div>
                        
                        <!-- 分析步骤显示 -->
                        <div id="analysisSteps">
                            <div class="analysis-placeholder">
                                <div class="text-center text-muted">
                                    <i class="bi bi-clock-history"></i>
                                    <p class="mt-2">等待开始分析...</p>
                                    <small>AI将为您透明化展示整个分析思考过程</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 策略确认区域 -->
                        <div id="strategyConfirmation" style="display: none;">
                            <div class="alert alert-info">
                                <h6>策略确认</h6>
                                <p>AI已分析出您的旅行策略，请确认是否继续：</p>
                                <button class="btn btn-success btn-sm" id="confirmStrategyBtn">确认继续</button>
                                <button class="btn btn-secondary btn-sm" id="modifyStrategyBtn">修改策略</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧规划面板 -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4><i class="bi bi-map"></i> 智能规划结果</h4>
                        <small class="text-muted">基于AI分析生成的详细行程</small>
                    </div>
                    <div class="card-body">
                        <!-- 视图切换 -->
                        <div class="btn-group mb-3" role="group">
                            <button type="button" class="btn btn-outline-primary active" id="itineraryViewBtn">
                                <i class="bi bi-calendar-event"></i> 行程视图
                            </button>
                            <button type="button" class="btn btn-outline-primary" id="routeViewBtn">
                                <i class="bi bi-geo-alt"></i> 路线视图
                            </button>
                        </div>
                        
                        <!-- 行程显示区域 -->
                        <div id="planningView">
                            <div id="itineraryView">
                                <div class="text-center text-muted">
                                    <i class="bi bi-hourglass-split"></i>
                                    <p class="mt-2">等待规划开始...</p>
                                    <small>完成分析后点击"立即规划"开始生成行程</small>
                                </div>
                            </div>
                            
                            <div id="routeView" style="display: none;">
                                <div class="text-center text-muted">
                                    <i class="bi bi-map"></i>
                                    <p class="mt-2">路线地图</p>
                                    <small>规划完成后将显示详细路线</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 应用JS -->
    <script src="/static/js/app-v3-refactored.js"></script>
    
    <script>
        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            const app = new AutoPilotAppV3Refactored();
            window.app = app; // 用于调试
        });
    </script>
</body>
</html>
