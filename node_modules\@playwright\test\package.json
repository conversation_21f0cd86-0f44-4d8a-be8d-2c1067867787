{"name": "@playwright/test", "version": "1.54.1", "description": "A high-level API to automate web browsers", "repository": {"type": "git", "url": "git+https://github.com/microsoft/playwright.git"}, "homepage": "https://playwright.dev", "engines": {"node": ">=18"}, "author": {"name": "Microsoft Corporation"}, "license": "Apache-2.0", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./index.js", "default": "./index.js"}, "./cli": "./cli.js", "./package.json": "./package.json", "./reporter": "./reporter.js"}, "bin": {"playwright": "cli.js"}, "scripts": {}, "dependencies": {"playwright": "1.54.1"}}