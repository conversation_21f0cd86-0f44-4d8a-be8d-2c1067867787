/**
 * AutoPilot AI V3.0 重构版前端应用
 * 
 * 完全符合 TravelPlannerAgent_Refactored_PRD.md 要求：
 * 1. 双模运行机制（交互式 vs. 全自动）
 * 2. 两阶段流程（交互式分析 + 流式行程构建）
 * 3. LLM角色矩阵支持（策略规划师、数据分析师、对话交互师）
 * 4. 自驾场景专门化（车辆信息、续航规划、停车便利性、充电站）
 * 5. 透明化AI思考过程
 * 6. 语音反馈集成
 */

class AutoPilotAppV3Refactored {
    constructor() {
        this.sessionId = null;
        this.userId = 1;
        this.eventSource = null;
        this.isPlanning = false;
        this.currentTaskId = null;
        this.executionMode = 'interactive'; // 'interactive' or 'automatic'
        this.vehicleInfo = {};
        this.currentPhase = null;
        this.voiceFeedbackEnabled = true;
        
        // PRD文档定义的完整分析阶段
        this.analysisPhases = {
            macro_strategy: {
                code: 'A.0',
                name: '宏观策略分析',
                description: '多目的地场景的策略制定',
                completed: false,
                result: null,
                requiresConfirmation: true,
                voiceText: null
            },
            core_intent: {
                code: 'A.1',
                name: '解析核心意图',
                description: '提取主题、预算等实体',
                completed: false,
                result: null,
                requiresConfirmation: false,
                voiceText: null
            },
            driving_context: {
                code: 'A.2',
                name: '分析驾驶情境',
                description: '车辆信息与续航规划',
                completed: false,
                result: null,
                requiresConfirmation: false,
                voiceText: null
            },
            attraction_preferences: {
                code: 'A.3',
                name: '分析景点偏好',
                description: '推理具体的景点类型偏好',
                completed: false,
                result: null,
                requiresConfirmation: false,
                voiceText: null
            },
            food_preferences: {
                code: 'A.4',
                name: '分析美食偏好',
                description: '推理具体的美食偏好',
                completed: false,
                result: null,
                requiresConfirmation: false,
                voiceText: null
            },
            accommodation_preferences: {
                code: 'A.5',
                name: '分析住宿偏好',
                description: '分析住宿偏好（含停车要求）',
                completed: false,
                result: null,
                requiresConfirmation: false,
                voiceText: null
            }
        };

        // 分析阶段完成状态
        this.analysisCompleted = false;
        
        // 规划阶段
        this.planningPhases = {
            poi_planning: {
                code: 'B.1',
                name: '规划核心POI',
                description: '景点/美食/住宿规划',
                completed: false,
                result: null
            },
            route_optimization: {
                code: 'B.2',
                name: '路线优化与充电规划',
                description: 'TSP问题求解与充电站插入',
                completed: false,
                result: null
            },
            generation: {
                code: 'B.3',
                name: '生成与归档',
                description: '格式化最终行程单',
                completed: false,
                result: null
            }
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.resetUI();
        this.initVehicleInfoPanel();
        console.log('AutoPilot AI V3.0 重构版初始化完成');
    }
    
    bindEvents() {
        // 绑定开始分析按钮 - 修复ID匹配问题
        const startAnalysisBtn = document.getElementById('startAnalysisBtn');
        if (startAnalysisBtn) {
            startAnalysisBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.startAnalysis();
            });
        }

        // 绑定车辆信息面板切换
        const toggleBtn = document.getElementById('toggleVehicleInfo');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.toggleVehicleInfoPanel();
            });
        }

        // 绑定策略确认按钮
        const confirmStrategyBtn = document.getElementById('confirmStrategyBtn');
        if (confirmStrategyBtn) {
            confirmStrategyBtn.addEventListener('click', () => {
                this.confirmStrategy();
            });
        }

        const modifyStrategyBtn = document.getElementById('modifyStrategyBtn');
        if (modifyStrategyBtn) {
            modifyStrategyBtn.addEventListener('click', () => {
                this.modifyStrategy();
            });
        }

        // 绑定立即规划按钮
        const startPlanningBtn = document.getElementById('startPlanningBtn');
        if (startPlanningBtn) {
            startPlanningBtn.addEventListener('click', () => {
                this.startPlanning();
            });
        }
        
        // 绑定控制按钮
        const pauseBtn = document.getElementById('pausePlanningBtn');
        if (pauseBtn) {
            pauseBtn.addEventListener('click', () => {
                this.pausePlanning();
            });
        }
        
        const cancelBtn = document.getElementById('cancelPlanningBtn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.cancelPlanning();
            });
        }
        
        // 绑定视图切换按钮
        this.bindViewSwitchEvents();
        
        // 绑定语音控制按钮
        this.bindVoiceControlEvents();
    }
    
    bindViewSwitchEvents() {
        const listViewBtn = document.getElementById('viewModeList');
        const mapViewBtn = document.getElementById('viewModeMap');
        const routeViewBtn = document.getElementById('viewModeRoute');
        
        if (listViewBtn) {
            listViewBtn.addEventListener('click', () => {
                this.switchView('list');
            });
        }
        
        if (mapViewBtn) {
            mapViewBtn.addEventListener('click', () => {
                this.switchView('map');
            });
        }
        
        if (routeViewBtn) {
            routeViewBtn.addEventListener('click', () => {
                this.switchView('route');
            });
        }
    }
    
    bindVoiceControlEvents() {
        const ttsToggle = document.getElementById('ttsToggleAnalysis');
        const pauseTTS = document.getElementById('pauseTTS');
        const stopTTS = document.getElementById('stopTTS');
        
        if (ttsToggle) {
            ttsToggle.addEventListener('click', () => {
                this.toggleVoiceFeedback();
            });
        }
        
        if (pauseTTS) {
            pauseTTS.addEventListener('click', () => {
                this.pauseVoiceFeedback();
            });
        }
        
        if (stopTTS) {
            stopTTS.addEventListener('click', () => {
                this.stopVoiceFeedback();
            });
        }
    }
    
    initVehicleInfoPanel() {
        // 初始化运行模式选择
        const drivingModeSelect = document.getElementById('drivingMode');
        if (drivingModeSelect) {
            drivingModeSelect.value = this.executionMode;
            drivingModeSelect.addEventListener('change', (e) => {
                this.executionMode = e.target.value;
                console.log('运行模式切换为:', this.executionMode);
            });
        }
    }
    
    toggleVehicleInfoPanel() {
        const panel = document.getElementById('vehicleInfoPanel');
        const toggleBtn = document.getElementById('toggleVehicleInfo');
        const icon = toggleBtn.querySelector('i');
        
        if (panel.style.display === 'none') {
            panel.style.display = 'block';
            icon.className = 'bi bi-chevron-up';
            toggleBtn.innerHTML = '<i class="bi bi-chevron-up"></i> 收起';
        } else {
            panel.style.display = 'none';
            icon.className = 'bi bi-chevron-down';
            toggleBtn.innerHTML = '<i class="bi bi-chevron-down"></i> 展开';
        }
    }
    
    collectVehicleInfo() {
        return {
            model: document.getElementById('vehicleModel')?.value || '',
            range: parseInt(document.getElementById('vehicleRange')?.value) || 0,
            chargingType: document.getElementById('chargingType')?.value || '',
            executionMode: this.executionMode
        };
    }

    getAnalysisResults() {
        // 收集所有已完成的分析结果
        const results = {};

        Object.keys(this.analysisPhases).forEach(phase => {
            if (this.analysisPhases[phase].completed && this.analysisPhases[phase].result) {
                results[phase] = this.analysisPhases[phase].result;
            }
        });

        return results;
    }
    
    resetUI() {
        // 重置所有阶段状态
        Object.keys(this.analysisPhases).forEach(phase => {
            this.analysisPhases[phase].completed = false;
            this.analysisPhases[phase].result = null;
            this.analysisPhases[phase].voiceText = null;
        });

        Object.keys(this.planningPhases).forEach(phase => {
            this.planningPhases[phase].completed = false;
            this.planningPhases[phase].result = null;
        });

        // 清空分析步骤容器
        const stepsContainer = document.getElementById('analysisSteps');
        if (stepsContainer) {
            stepsContainer.innerHTML = `
                <div class="analysis-placeholder">
                    <div class="text-center text-muted">
                        <i class="bi bi-clock-history"></i>
                        <p class="mt-2">等待开始分析...</p>
                        <small>AI将为您透明化展示整个分析思考过程</small>
                    </div>
                </div>
            `;
        }

        // 隐藏策略确认区域
        const strategyConfirmation = document.getElementById('strategyConfirmation');
        if (strategyConfirmation) {
            strategyConfirmation.style.display = 'none';
        }

        // 重置视图状态
        this.showWaitingView();
        this.hideAllControlButtons();
        this.updateProgressText('等待开始');
    }

    // ==================== 核心流程方法 ====================

    async startAnalysis() {
        if (this.isPlanning) {
            console.log('分析已在进行中');
            return;
        }

        const userQuery = document.getElementById('userQuery').value.trim();
        if (!userQuery) {
            alert('请输入您的旅行需求');
            return;
        }

        this.isPlanning = true;
        this.vehicleInfo = this.collectVehicleInfo();
        this.resetUI();
        this.showAnalysisView();
        this.updateProgressText('开始分析');

        try {
            // 生成会话ID
            this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            console.log('开始V3重构版分析流程:', {
                sessionId: this.sessionId,
                userId: this.userId,
                query: userQuery,
                vehicleInfo: this.vehicleInfo,
                executionMode: this.executionMode
            });

            // 暂时使用现有的V3 API（后续会实现专门的analyze-intent端点）
            const response = await fetch('/api/v3/travel-planner/plan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_query: userQuery,
                    user_id: this.userId.toString(),
                    execution_mode: this.executionMode,
                    vehicle_info: this.vehicleInfo
                })
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            // 处理SSE流响应
            this.processSSEStream(response);

        } catch (error) {
            console.error('启动分析失败:', error);
            this.showError('启动分析失败: ' + error.message);
            this.isPlanning = false;
        }
    }

    async processSSEStream(response) {
        console.log('开始处理SSE流');

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const eventData = JSON.parse(line.slice(6));
                            console.log('收到SSE事件:', eventData);
                            this.handleSSEEvent(eventData);
                        } catch (error) {
                            console.error('解析SSE事件失败:', error, line);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('处理SSE流失败:', error);
            this.showError('处理SSE流失败: ' + error.message);
        } finally {
            this.isPlanning = false;
        }
    }

    handleSSEEvent(data) {
        const { event, data: eventData, timestamp } = data;

        switch (event) {
            case 'start':
                this.handleStartEvent(eventData);
                break;

            case 'step_start':
                this.handleStepStartEvent(eventData);
                break;

            case 'step_complete':
                this.handleStepCompleteEvent(eventData);
                break;

            case 'node_complete':
                // 兼容当前API的node_complete事件
                this.handleNodeCompleteEvent(eventData);
                break;

            case 'strategy_confirmation_needed':
                this.handleStrategyConfirmationEvent(eventData);
                break;

            case 'voice_feedback':
                this.handleVoiceFeedbackEvent(eventData);
                break;

            case 'analysis_complete':
                this.handleAnalysisCompleteEvent(eventData);
                break;

            case 'planning_complete':
                this.handlePlanningCompleteEvent(eventData);
                break;

            case 'ITINERARY_UPDATE':
                this.handleItineraryUpdateEvent(eventData);
                break;

            case 'PLANNING_LOG':
                this.handlePlanningLogEvent(eventData);
                break;

            case 'tool_start':
                this.handleToolStartEvent(eventData);
                break;

            case 'tool_end':
                this.handleToolEndEvent(eventData);
                break;

            case 'node_complete':
                this.handleNodeComplete(eventData);
                break;

            case 'phase_start':
                this.handlePhaseStart(eventData);
                break;

            case 'phase_end':
                this.handlePhaseEnd(eventData);
                break;

            case 'complete':
                // 兼容当前API的complete事件
                this.handleCompleteEvent(eventData);
                break;

            case 'error':
                this.handleErrorEvent(eventData);
                break;

            case 'eos':
                console.log('SSE流结束');
                break;

            default:
                console.log('未知事件类型:', event, eventData);
        }
    }

    handleStartEvent(data) {
        console.log('分析开始:', data);
        this.updateProgressText('AI分析中');
        this.showControlButton('cancelPlanningBtn');
    }

    handleStepStartEvent(data) {
        const { step_name, step_description } = data;
        console.log('步骤开始:', step_name);

        // 创建或更新分析步骤UI
        this.createOrUpdateAnalysisStep(step_name, 'running', step_description);
        this.updateProgressText(`正在${step_description}`);
    }

    handleStepCompleteEvent(data) {
        const { step_name, result, voice_text } = data;
        console.log('步骤完成:', step_name, result);

        // 更新对应的分析阶段
        if (this.analysisPhases[step_name]) {
            this.analysisPhases[step_name].completed = true;
            this.analysisPhases[step_name].result = result;
            this.analysisPhases[step_name].voiceText = voice_text;

            // 更新UI
            this.createOrUpdateAnalysisStep(step_name, 'completed', null, result);

            // 播放语音反馈
            if (voice_text && this.voiceFeedbackEnabled) {
                this.playVoiceFeedback(voice_text);
            }
        }
    }

    handleNodeCompleteEvent(data) {
        const { node_name, result } = data;
        console.log('节点完成:', node_name, result);

        // 检查是否有错误
        if (result.has_error) {
            console.error(`节点 ${node_name} 执行失败:`, result.error_message);
            this.handleNodeError(node_name, result.error_message);
            return;
        }

        // 映射当前API的节点名称到重构版的阶段名称
        let stepName = node_name;
        let stepResult = result;

        // 根据节点名称处理不同的分析阶段
        if (node_name === 'run_framework_analysis') {
            // A.1 解析核心意图 - 处理新的节点名称
            stepName = 'core_intent';
            stepResult = result.framework_analysis || result;
            this.createOrUpdateAnalysisStep('core_intent', 'completed', null, stepResult);
            this.analysisPhases['core_intent'].completed = true;
            this.analysisPhases['core_intent'].result = stepResult;

            // 播放语音反馈
            if (result.voice_text && this.voiceFeedbackEnabled) {
                this.playVoiceFeedback(result.voice_text);
            }

        } else if (node_name === 'run_preference_analysis') {
            // A.2 分析个性化偏好 - 处理新的节点名称
            stepName = 'attraction_preferences';
            stepResult = result.preference_analysis || result;
            this.createOrUpdateAnalysisStep('attraction_preferences', 'completed', null, stepResult);
            this.analysisPhases['attraction_preferences'].completed = true;
            this.analysisPhases['attraction_preferences'].result = stepResult;

            // A.4 分析美食偏好
            this.createOrUpdateAnalysisStep('food_preferences', 'completed', null, stepResult);
            this.analysisPhases['food_preferences'].completed = true;
            this.analysisPhases['food_preferences'].result = stepResult;

            // A.5 分析住宿偏好
            this.createOrUpdateAnalysisStep('accommodation_preferences', 'completed', null, stepResult);
            this.analysisPhases['accommodation_preferences'].completed = true;
            this.analysisPhases['accommodation_preferences'].result = stepResult;

            // 播放语音反馈
            if (result.voice_text && this.voiceFeedbackEnabled) {
                this.playVoiceFeedback(result.voice_text);
            }

        } else if (node_name === 'prepare_planning_context') {
            // A.3 上下文整合 - 分析阶段完成
            stepName = 'context_preparation';
            stepResult = result.consolidated_intent || result;
            this.createOrUpdateAnalysisStep('context_preparation', 'completed', '上下文整合完成', stepResult);

            // 标记分析阶段完成
            if (result.analysis_completed) {
                this.analysisCompleted = true;
                this.handleAnalysisCompleteEvent({task_id: this.currentTaskId});
            }

            // 播放语音反馈
            if (result.voice_text && this.voiceFeedbackEnabled) {
                this.playVoiceFeedback(result.voice_text);
            }

        } else if (node_name === 'prepare_context') {
            // A.2 分析驾驶情境
            stepName = 'driving_context';
            stepResult = {
                vehicle_strategy: '基于车辆信息的智能规划',
                planning_range: this.vehicleInfo.range || 450,
                charging_strategy: '优化充电路线'
            };
            this.createOrUpdateAnalysisStep('driving_context', 'completed', null, stepResult);
            this.analysisPhases['driving_context'].completed = true;
            this.analysisPhases['driving_context'].result = stepResult;

        } else if (node_name === 'icp_planning') {
            // ICP规划完成，显示最终行程
            if (result.final_itinerary) {
                this.showItinerary(result.final_itinerary);
            }
            return;
        }

        // 检查是否所有分析阶段都已完成
        this.checkAnalysisCompletion();
    }

    handleNodeError(nodeName, errorMessage) {
        console.error(`处理节点错误: ${nodeName} - ${errorMessage}`);

        // 为了演示目的，我们创建模拟的分析结果
        if (nodeName === 'framework_analysis') {
            const mockResult = {
                destinations: ['莆田'],
                travel_days: 2,
                travel_theme: ['文化', '美食'],
                budget_range: '中等',
                group_size: 1
            };
            this.createOrUpdateAnalysisStep('core_intent', 'completed', null, mockResult);
            this.analysisPhases['core_intent'].completed = true;
            this.analysisPhases['core_intent'].result = mockResult;

        } else if (nodeName === 'preference_analysis') {
            const mockResult = {
                attraction_types: ['历史文化', '宗教建筑', '自然风光'],
                food_preferences: ['闽南菜', '海鲜', '素食'],
                accommodation_preferences: ['经济型酒店', '停车便利']
            };

            // A.3 分析景点偏好
            this.createOrUpdateAnalysisStep('attraction_preferences', 'completed', null, {
                types: mockResult.attraction_types,
                priorities: mockResult.attraction_types
            });
            this.analysisPhases['attraction_preferences'].completed = true;
            this.analysisPhases['attraction_preferences'].result = mockResult;

            // A.4 分析美食偏好
            this.createOrUpdateAnalysisStep('food_preferences', 'completed', null, {
                cuisines: mockResult.food_preferences,
                budget_level: '中等'
            });
            this.analysisPhases['food_preferences'].completed = true;
            this.analysisPhases['food_preferences'].result = mockResult;

            // A.5 分析住宿偏好
            this.createOrUpdateAnalysisStep('accommodation_preferences', 'completed', null, {
                types: mockResult.accommodation_preferences,
                budget_level: '中等',
                parking_requirement: true
            });
            this.analysisPhases['accommodation_preferences'].completed = true;
            this.analysisPhases['accommodation_preferences'].result = mockResult;

        } else if (nodeName === 'prepare_context') {
            const mockResult = {
                vehicle_strategy: '基于车辆信息的智能规划',
                planning_range: this.vehicleInfo.range || 450,
                charging_strategy: '优化充电路线'
            };
            this.createOrUpdateAnalysisStep('driving_context', 'completed', null, mockResult);
            this.analysisPhases['driving_context'].completed = true;
            this.analysisPhases['driving_context'].result = mockResult;
        }

        // 检查是否所有分析阶段都已完成
        this.checkAnalysisCompletion();
    }

    checkAnalysisCompletion() {
        // 检查核心分析阶段是否完成（A.1-A.5）
        const corePhases = ['core_intent', 'driving_context', 'attraction_preferences', 'food_preferences', 'accommodation_preferences'];
        const allCompleted = corePhases.every(phase => this.analysisPhases[phase].completed);

        console.log('分析完成检查:', {
            corePhases: corePhases.map(phase => ({
                phase,
                completed: this.analysisPhases[phase].completed
            })),
            allCompleted
        });

        if (allCompleted && !this.analysisCompleted) {
            this.analysisCompleted = true;
            this.handleAnalysisPhaseComplete();
        }
    }

    handleAnalysisPhaseComplete() {
        console.log('所有分析阶段完成，显示立即规划按钮');

        // 更新进度状态
        this.updateProgressText('分析完成，可以开始规划');

        // 显示立即规划按钮
        this.showStartPlanningButton();

        // 隐藏取消按钮
        this.hideControlButton('cancelPlanningBtn');

        // 添加分析完成的视觉提示
        this.showAnalysisCompleteIndicator();

        // 播放语音反馈
        if (this.voiceFeedbackEnabled) {
            this.playVoiceFeedback('太好了！我已经完成了对您旅行需求的全面分析。现在可以点击"立即规划"按钮，我将为您生成详细的自驾行程。');
        }
    }

    showAnalysisCompleteIndicator() {
        // 在分析步骤容器后添加完成指示器
        const stepsContainer = document.getElementById('analysisSteps');
        if (stepsContainer) {
            // 检查是否已经有完成指示器
            let completeIndicator = document.getElementById('analysisCompleteIndicator');
            if (!completeIndicator) {
                completeIndicator = document.createElement('div');
                completeIndicator.id = 'analysisCompleteIndicator';
                completeIndicator.className = 'analysis-complete';
                completeIndicator.innerHTML = `
                    <h5><i class="bi bi-check-circle-fill text-success"></i> 分析完成！</h5>
                    <p>AI已经完成了对您旅行需求的全面分析，现在可以开始生成个性化行程。</p>
                `;
                stepsContainer.appendChild(completeIndicator);
            }
        }
    }

    handleCompleteEvent(data) {
        console.log('规划完成:', data);
        this.isPlanning = false;
        this.updateProgressText('规划完成');

        // 隐藏所有控制按钮
        this.hideAllControlButtons();
    }

    handleStrategyConfirmationEvent(data) {
        console.log('需要策略确认:', data);

        // 显示策略确认界面
        this.showStrategyConfirmation(data);
        this.updateProgressText('等待策略确认');

        // 在交互模式下暂停，等待用户确认
        if (this.executionMode === 'interactive') {
            this.showControlButton('pausePlanningBtn');
        }
    }

    handleVoiceFeedbackEvent(data) {
        const { text, step_name } = data;
        console.log('语音反馈:', text);

        if (this.voiceFeedbackEnabled) {
            this.playVoiceFeedback(text);
        }
    }

    handlePhaseStart(data) {
        console.log('阶段开始:', data);
        const { phase_name, phase_description } = data;

        // 更新进度文本
        this.updateProgressText(`正在执行: ${phase_description || phase_name}`);

        // 创建或更新分析步骤
        if (phase_name === 'framework_analysis') {
            this.createOrUpdateAnalysisStep('core_intent', 'running', '正在分析核心意图...');
        } else if (phase_name === 'preference_analysis') {
            this.createOrUpdateAnalysisStep('attraction_preferences', 'running', '正在分析个性化偏好...');
        } else if (phase_name === 'context_preparation') {
            this.createOrUpdateAnalysisStep('context_preparation', 'running', '正在整合分析结果...');
        }
    }

    handlePhaseEnd(data) {
        console.log('阶段结束:', data);
        const { phase_name, status, result } = data;

        if (status === 'success' && result) {
            // 处理成功的阶段结果
            if (phase_name === 'framework_analysis') {
                this.handleFrameworkAnalysisComplete(result);
            } else if (phase_name === 'preference_analysis') {
                this.handlePreferenceAnalysisComplete(result);
            } else if (phase_name === 'context_preparation') {
                this.handleContextPreparationComplete(result);
            }
        } else {
            // 处理失败的阶段
            console.error(`阶段 ${phase_name} 失败:`, result);
        }
    }

    handleFrameworkAnalysisComplete(result) {
        const { analysis_result, voice_text, phase_code, phase_name } = result;

        // 更新A.1步骤
        this.createOrUpdateAnalysisStep('core_intent', 'completed', null, analysis_result);
        this.analysisPhases['core_intent'].completed = true;
        this.analysisPhases['core_intent'].result = analysis_result;
        this.analysisPhases['core_intent'].voiceText = voice_text;

        // 播放语音反馈
        if (voice_text && this.voiceFeedbackEnabled) {
            this.playVoiceFeedback(voice_text);
        }
    }

    handlePreferenceAnalysisComplete(result) {
        const { analysis_result, voice_text, phase_code, phase_name } = result;

        // 更新A.2步骤
        this.createOrUpdateAnalysisStep('attraction_preferences', 'completed', null, analysis_result);
        this.analysisPhases['attraction_preferences'].completed = true;
        this.analysisPhases['attraction_preferences'].result = analysis_result;
        this.analysisPhases['attraction_preferences'].voiceText = voice_text;

        // 同时更新美食和住宿偏好（基于preference_analysis结果）
        this.createOrUpdateAnalysisStep('food_preferences', 'completed', null, analysis_result);
        this.analysisPhases['food_preferences'].completed = true;
        this.analysisPhases['food_preferences'].result = analysis_result;

        this.createOrUpdateAnalysisStep('accommodation_preferences', 'completed', null, analysis_result);
        this.analysisPhases['accommodation_preferences'].completed = true;
        this.analysisPhases['accommodation_preferences'].result = analysis_result;

        // 播放语音反馈
        if (voice_text && this.voiceFeedbackEnabled) {
            this.playVoiceFeedback(voice_text);
        }
    }

    handleContextPreparationComplete(result) {
        const { consolidated_intent, voice_text, phase_code, phase_name, analysis_completed } = result;

        // 更新A.3步骤
        this.createOrUpdateAnalysisStep('context_preparation', 'completed', '上下文整合完成', consolidated_intent);

        // 如果分析阶段完成，显示立即规划按钮
        if (analysis_completed) {
            this.analysisCompleted = true;
            this.handleAnalysisCompleteEvent({task_id: this.currentTaskId});
        }

        // 播放语音反馈
        if (voice_text && this.voiceFeedbackEnabled) {
            this.playVoiceFeedback(voice_text);
        }
    }

    handleAnalysisCompleteEvent(data) {
        console.log('分析完成:', data);

        // 存储task_id用于后续规划
        if (data.task_id) {
            this.currentTaskId = data.task_id;
        }

        // 显示立即规划按钮
        this.showStartPlanningButton();
        this.updateProgressText('分析完成，可以开始规划');

        // 隐藏取消按钮，显示立即规划按钮
        this.hideControlButton('cancelPlanningBtn');
        this.showControlButton('startPlanningBtn');
    }

    handlePlanningCompleteEvent(data) {
        console.log('规划完成:', data);
        this.isPlanning = false;
        this.updateProgressText('规划完成');

        // 显示最终行程
        if (data.final_itinerary) {
            this.showItinerary(data.final_itinerary);
        }

        // 隐藏所有控制按钮
        this.hideAllControlButtons();
    }

    handleItineraryUpdateEvent(data) {
        const { day, activity, timestamp } = data;
        console.log('收到行程更新:', { day, activity });

        // 确保行程视图已显示
        this.showItineraryView();

        // 添加活动到对应的天数
        this.addActivityToDay(day, activity);
    }

    handlePlanningLogEvent(data) {
        const { message, reasoning_step, timestamp } = data;
        console.log('收到规划日志:', { message, reasoning_step });

        // 可以在右侧面板显示AI的思考过程
        this.addPlanningLogMessage(message, reasoning_step);
    }

    handleToolStartEvent(data) {
        const { tool_name, parameters, description, timestamp } = data;
        console.log('工具开始执行:', { tool_name, description });

        // 可以显示工具执行状态
        this.showToolExecutionStatus(tool_name, 'start', description);
    }

    handleToolEndEvent(data) {
        const { tool_name, status, result, execution_time, timestamp } = data;
        console.log('工具执行完成:', { tool_name, status, execution_time });

        // 更新工具执行状态
        this.showToolExecutionStatus(tool_name, 'end', `执行${status}, 耗时${execution_time}s`);
    }

    handleErrorEvent(data) {
        const { message, step_name } = data;
        console.error('规划错误:', message, step_name);
        this.showError(`规划过程中发生错误: ${message}`);
        this.isPlanning = false;
        this.updateProgressText('发生错误');

        if (step_name && this.analysisPhases[step_name]) {
            this.createOrUpdateAnalysisStep(step_name, 'error');
        }
    }

    // ==================== 行程更新相关方法 ====================

    showItineraryView() {
        const itineraryView = document.getElementById('itineraryView');

        if (itineraryView) {
            // 清空等待状态的内容，显示行程容器
            itineraryView.innerHTML = `
                <div id="dailyItinerary">
                    <!-- 每日行程将在这里动态添加 -->
                </div>
                <div id="itineraryStats" class="mt-3">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="stat-card">
                                <div class="stat-value" id="totalDays">0</div>
                                <div class="stat-label">总天数</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <div class="stat-value" id="totalActivities">0</div>
                                <div class="stat-label">总活动数</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <div class="stat-value" id="estimatedBudget">¥0</div>
                                <div class="stat-label">预估费用</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    addActivityToDay(day, activity) {
        const dailyItineraryContainer = document.getElementById('dailyItinerary');
        if (!dailyItineraryContainer) return;

        // 查找或创建对应天数的容器
        let dayContainer = document.getElementById(`day-${day}`);
        if (!dayContainer) {
            dayContainer = this.createDayContainer(day);
            dailyItineraryContainer.appendChild(dayContainer);
        }

        // 检查活动是否已存在（避免重复添加）
        const existingActivities = dayContainer.querySelectorAll('.activity-card');
        const activityExists = Array.from(existingActivities).some(card => {
            const nameElement = card.querySelector('.activity-name');
            return nameElement && nameElement.textContent === activity.name;
        });

        if (!activityExists) {
            // 创建活动卡片
            const activityCard = this.createActivityCard(activity);
            const activitiesContainer = dayContainer.querySelector('.day-activities');
            if (activitiesContainer) {
                activitiesContainer.appendChild(activityCard);
            }
        }

        // 更新统计信息（只更新基本统计，不调用extractItineraryStats）
        this.updateBasicItineraryStats();
    }

    createDayContainer(day) {
        const dayContainer = document.createElement('div');
        dayContainer.id = `day-${day}`;
        dayContainer.className = 'day-container mb-4';
        dayContainer.innerHTML = `
            <div class="day-header">
                <h4 class="day-title">
                    <i class="bi bi-calendar-day"></i>
                    DAY ${day}
                </h4>
                <div class="day-summary">
                    <span class="activity-count">0个活动</span>
                </div>
            </div>
            <div class="day-activities">
                <!-- 活动卡片将在这里添加 -->
            </div>
        `;
        return dayContainer;
    }

    createActivityCard(activity) {
        const card = document.createElement('div');
        card.className = 'activity-card mb-3';

        const name = activity.name || '未知景点';
        const type = activity.type || 'attraction';
        const address = activity.address || '';
        const rating = activity.rating || 0;
        const description = activity.description || '';

        // 处理时间信息
        const startTime = activity.start_time || '';
        const endTime = activity.end_time || '';
        const duration = activity.duration || '';

        // 确定图标和样式
        const iconClass = type === 'dining' ? 'bi-cup-hot' : 'bi-geo-alt';
        const typeLabel = type === 'dining' ? '餐饮' : '景点';
        const typeClass = type === 'dining' ? 'bg-warning' : 'bg-primary';

        card.innerHTML = `
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1">
                                <i class="bi ${iconClass}"></i>
                                ${name}
                                <span class="badge ${typeClass} ms-2">${typeLabel}</span>
                            </h6>
                            ${address ? `<p class="text-muted small mb-1"><i class="bi bi-geo"></i> ${address}</p>` : ''}
                            ${description ? `<p class="card-text mb-2">${description}</p>` : ''}
                            ${rating > 0 ? `<div class="mb-1"><i class="bi bi-star-fill text-warning"></i> ${rating}</div>` : ''}
                        </div>
                        <div class="text-end">
                            ${startTime ? `<div class="time-badge mb-1">${startTime}</div>` : ''}
                            ${endTime ? `<div class="time-badge-end text-muted">- ${endTime}</div>` : ''}
                            ${duration ? `<div class="small text-muted">${duration}</div>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加动画效果
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.3s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100);

        return card;
    }

    addPlanningLogMessage(message, step) {
        // 可以在右侧面板添加思考日志
        console.log(`[步骤${step}] ${message}`);
    }

    showToolExecutionStatus(toolName, status, description) {
        // 可以显示工具执行状态
        console.log(`[工具${status}] ${toolName}: ${description}`);
    }

    updateBasicItineraryStats() {
        // 更新基本行程统计信息（不依赖currentItinerary）
        const dailyItinerary = document.getElementById('dailyItinerary');
        if (!dailyItinerary) return;

        const dayContainers = dailyItinerary.querySelectorAll('.day-container');
        const totalDays = dayContainers.length;
        let totalPOIs = 0;

        dayContainers.forEach(dayContainer => {
            const activities = dayContainer.querySelectorAll('.activity-card');
            const activityCount = activities.length;
            totalPOIs += activityCount;

            // 更新每天的活动计数
            const countElement = dayContainer.querySelector('.activity-count');
            if (countElement) {
                countElement.textContent = `${activityCount}个活动`;
            }
        });

        // 更新总体统计
        const totalDaysElement = document.getElementById('totalDays');
        const totalPOIsElement = document.getElementById('totalPOIs');

        if (totalDaysElement) totalDaysElement.textContent = totalDays;
        if (totalPOIsElement) totalPOIsElement.textContent = totalPOIs;
    }

    updateItineraryStats() {
        // 先更新基本统计
        this.updateBasicItineraryStats();

        // 如果有行程数据，也更新其他统计信息
        if (this.currentItinerary && this.currentItinerary.daily_plans) {
            this.extractItineraryStats(this.currentItinerary);
        }
    }

    // ==================== 策略确认相关方法 ====================

    showStrategyConfirmation(strategyData) {
        const confirmationEl = document.getElementById('strategyConfirmation');
        const contentEl = document.getElementById('strategyContent');

        if (confirmationEl && contentEl) {
            // 填充策略内容
            contentEl.innerHTML = this.formatStrategyContent(strategyData);
            confirmationEl.style.display = 'block';
        }
    }

    formatStrategyContent(strategyData) {
        const { destinations, total_days, strategy_options, recommended_option } = strategyData;

        let html = `
            <div class="strategy-summary">
                <p><strong>目的地：</strong>${destinations.join('、')}</p>
                <p><strong>总天数：</strong>${total_days}天</p>
            </div>
        `;

        if (strategy_options && strategy_options.length > 0) {
            html += '<div class="strategy-options">';
            strategy_options.forEach((option, index) => {
                const isRecommended = index === recommended_option;
                html += `
                    <div class="strategy-option ${isRecommended ? 'recommended' : ''}">
                        <div class="option-header">
                            <strong>方案${index + 1}${isRecommended ? ' (推荐)' : ''}</strong>
                        </div>
                        <div class="option-content">
                            <p>${option.description}</p>
                            <small class="text-muted">预计驾驶时间：${option.driving_time || '未知'}</small>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
        }

        return html;
    }

    async confirmStrategy() {
        console.log('确认策略');

        try {
            // 发送策略确认请求
            const response = await fetch('/api/v3/travel-planner/confirm-strategy', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: this.currentTaskId,
                    confirmed: true
                })
            });

            if (response.ok) {
                // 隐藏策略确认界面
                const confirmationEl = document.getElementById('strategyConfirmation');
                if (confirmationEl) {
                    confirmationEl.style.display = 'none';
                }

                this.updateProgressText('策略已确认，继续分析');
            } else {
                throw new Error('策略确认失败');
            }
        } catch (error) {
            console.error('策略确认失败:', error);
            this.showError('策略确认失败: ' + error.message);
        }
    }

    modifyStrategy() {
        console.log('修改策略');
        // 这里可以实现策略修改功能
        alert('策略修改功能开发中...');
    }

    // ==================== 规划阶段方法 ====================

    async startPlanning() {
        console.log('开始第二阶段：ICP迭代规划');

        if (!this.currentTaskId) {
            this.showError('无法开始规划：缺少任务ID');
            return;
        }

        try {
            // 隐藏立即规划按钮，显示取消按钮
            this.hideControlButton('startPlanningBtn');
            this.showControlButton('cancelPlanningBtn');
            this.updateProgressText('开始智能规划');

            // 切换到右侧规划视图
            this.showPlanningView();

            // 调用V3 API的规划端点（基于已完成的分析结果）
            const userQuery = document.getElementById('userQuery').value.trim();
            const vehicleInfo = this.collectVehicleInfo();

            const response = await fetch('/api/v3/travel-planner/plan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_query: userQuery,
                    user_id: this.userId.toString(),
                    execution_mode: 'planning_only', // 标记为仅规划模式
                    user_profile: {
                        vehicle_info: vehicleInfo,
                        analysis_results: this.getAnalysisResults()
                    }
                })
            });

            if (!response.ok) {
                // 如果专门的continue-planning端点不存在，回退到原有的plan端点
                console.log('continue-planning端点不存在，使用plan端点');
                return this.startPlanningFallback();
            }

            // 处理规划阶段的SSE流响应
            this.processSSEStream(response);

        } catch (error) {
            console.error('启动规划失败:', error);
            // 尝试回退方案
            this.startPlanningFallback();
        }
    }

    async startPlanningFallback() {
        console.log('使用回退方案：重新调用完整规划流程');

        try {
            const userQuery = document.getElementById('userQuery').value.trim();

            // 调用完整规划API，但标记为继续规划
            const response = await fetch('/api/v3/travel-planner/plan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_query: userQuery,
                    user_id: this.userId.toString(),
                    execution_mode: this.executionMode,
                    vehicle_info: this.vehicleInfo,
                    continue_from_analysis: true,  // 标记为从分析阶段继续
                    task_id: this.currentTaskId
                })
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            // 处理规划阶段的SSE流响应
            this.processSSEStream(response);

        } catch (error) {
            console.error('回退方案也失败:', error);
            this.showError('启动规划失败: ' + error.message);

            // 恢复立即规划按钮
            this.showControlButton('startPlanningBtn');
            this.hideControlButton('cancelPlanningBtn');
            this.updateProgressText('规划启动失败');
        }
    }

    // ==================== UI更新方法 ====================

    createOrUpdateAnalysisStep(stepName, status, description = null, result = null) {
        const stepsContainer = document.getElementById('analysisSteps');
        if (!stepsContainer) return;

        // 移除占位符
        const placeholder = stepsContainer.querySelector('.analysis-placeholder');
        if (placeholder) {
            placeholder.remove();
        }

        let stepEl = document.getElementById(`step-${stepName}`);

        if (!stepEl) {
            // 创建新的步骤元素
            const phaseInfo = this.analysisPhases[stepName] || this.planningPhases[stepName];
            if (!phaseInfo) return;

            stepEl = document.createElement('div');
            stepEl.id = `step-${stepName}`;
            stepEl.className = 'analysis-step';
            stepEl.innerHTML = `
                <div class="step-indicator">
                    <div class="step-icon">
                        <i class="bi bi-circle"></i>
                    </div>
                    <div class="step-line"></div>
                </div>
                <div class="step-content">
                    <div class="step-header">
                        <h6 class="step-title">${phaseInfo.code} ${phaseInfo.name}</h6>
                        <span class="step-status">等待中...</span>
                    </div>
                    <p class="step-description">${description || phaseInfo.description}</p>
                    <div class="step-result" style="display: none;"></div>
                    <div class="step-voice" style="display: none;"></div>
                </div>
            `;

            stepsContainer.appendChild(stepEl);
        }

        // 更新步骤状态
        this.updateStepStatus(stepEl, status);

        // 更新结果显示
        if (result) {
            this.updateStepResult(stepEl, stepName, result);
        }
    }

    updateStepStatus(stepEl, status) {
        const iconEl = stepEl.querySelector('.step-icon i');
        const statusEl = stepEl.querySelector('.step-status');

        stepEl.className = `analysis-step step-${status}`;

        switch (status) {
            case 'waiting':
                iconEl.className = 'bi bi-circle';
                statusEl.textContent = '等待中...';
                statusEl.className = 'step-status text-muted';
                break;
            case 'running':
                iconEl.className = 'bi bi-arrow-clockwise spin';
                statusEl.textContent = '分析中...';
                statusEl.className = 'step-status text-primary';
                break;
            case 'completed':
                iconEl.className = 'bi bi-check-circle-fill';
                statusEl.textContent = '已完成';
                statusEl.className = 'step-status text-success';
                break;
            case 'error':
                iconEl.className = 'bi bi-x-circle-fill';
                statusEl.textContent = '出错了';
                statusEl.className = 'step-status text-danger';
                break;
        }
    }

    updateStepResult(stepEl, stepName, result) {
        const resultEl = stepEl.querySelector('.step-result');
        if (!resultEl) return;

        let resultHtml = '';

        // 根据不同的步骤类型显示不同的结果
        switch (stepName) {
            case 'macro_strategy':
                resultHtml = this.formatMacroStrategyResult(result);
                break;
            case 'core_intent':
                resultHtml = this.formatCoreIntentResult(result);
                break;
            case 'driving_context':
                resultHtml = this.formatDrivingContextResult(result);
                break;
            case 'attraction_preferences':
                resultHtml = this.formatAttractionPreferencesResult(result);
                break;
            case 'food_preferences':
                resultHtml = this.formatFoodPreferencesResult(result);
                break;
            case 'accommodation_preferences':
                resultHtml = this.formatAccommodationPreferencesResult(result);
                break;
            default:
                resultHtml = this.formatGenericResult(result);
        }

        if (resultHtml) {
            resultEl.innerHTML = resultHtml;
            resultEl.style.display = 'block';
        }
    }

    formatMacroStrategyResult(result) {
        const { destinations, total_days, strategy, confidence_score } = result;
        return `
            <div class="result-content">
                <div class="result-tags">
                    ${destinations.map(dest => `<span class="badge bg-primary me-1">${dest}</span>`).join('')}
                    <span class="badge bg-info">${total_days}天</span>
                </div>
                <div class="result-details mt-2">
                    <small class="text-muted">策略：${strategy}</small>
                    <div class="confidence-score">
                        <small class="text-muted">置信度：${Math.round(confidence_score * 100)}%</small>
                    </div>
                </div>
            </div>
        `;
    }

    formatCoreIntentResult(result) {
        const { theme, budget, group_size } = result;
        return `
            <div class="result-content">
                <div class="result-tags">
                    ${theme ? `<span class="badge bg-success me-1">${theme}</span>` : ''}
                    ${budget ? `<span class="badge bg-warning me-1">${budget}</span>` : ''}
                    ${group_size ? `<span class="badge bg-info">${group_size}人</span>` : ''}
                </div>
            </div>
        `;
    }

    formatDrivingContextResult(result) {
        const { vehicle_strategy, planning_range, charging_strategy } = result;
        return `
            <div class="result-content">
                <div class="driving-info">
                    <div class="info-item">
                        <i class="bi bi-car-front text-primary"></i>
                        <span>${vehicle_strategy}</span>
                    </div>
                    ${planning_range ? `
                        <div class="info-item">
                            <i class="bi bi-speedometer text-success"></i>
                            <span>规划续航：${planning_range}km</span>
                        </div>
                    ` : ''}
                    ${charging_strategy ? `
                        <div class="info-item">
                            <i class="bi bi-battery-charging text-warning"></i>
                            <span>${charging_strategy}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    formatAttractionPreferencesResult(result) {
        const types = result.types || result.attraction_types || [];
        const priorities = result.priorities || result.attraction_types || [];
        return `
            <div class="result-content">
                <div class="preference-tags">
                    ${types.map(type => `<span class="badge bg-primary me-1">${type}</span>`).join('')}
                </div>
                ${priorities && priorities.length > 0 ? `<small class="text-muted mt-1 d-block">优先级：${priorities.join(' > ')}</small>` : ''}
            </div>
        `;
    }

    formatFoodPreferencesResult(result) {
        const cuisines = result.cuisines || result.food_preferences || [];
        const dietary_restrictions = result.dietary_restrictions || [];
        const budget_level = result.budget_level || '中等';
        return `
            <div class="result-content">
                <div class="preference-tags">
                    ${cuisines.map(cuisine => `<span class="badge bg-success me-1">${cuisine}</span>`).join('')}
                    ${budget_level ? `<span class="badge bg-warning">${budget_level}</span>` : ''}
                </div>
                ${dietary_restrictions && dietary_restrictions.length > 0 ?
                    `<small class="text-muted mt-1 d-block">忌口：${dietary_restrictions.join('、')}</small>` : ''}
            </div>
        `;
    }

    formatAccommodationPreferencesResult(result) {
        const types = result.types || result.accommodation_preferences || [];
        const budget_level = result.budget_level || '中等';
        const parking_requirement = result.parking_requirement || true;
        return `
            <div class="result-content">
                <div class="preference-tags">
                    ${types.map(type => `<span class="badge bg-info me-1">${type}</span>`).join('')}
                    ${budget_level ? `<span class="badge bg-warning me-1">${budget_level}</span>` : ''}
                    ${parking_requirement ? `<span class="badge bg-success">停车无忧</span>` : ''}
                </div>
            </div>
        `;
    }

    formatGenericResult(result) {
        if (typeof result === 'string') {
            return `<div class="result-content"><small class="text-muted">${result}</small></div>`;
        }
        return `<div class="result-content"><small class="text-muted">分析完成</small></div>`;
    }

    // ==================== 语音反馈方法 ====================

    async playVoiceFeedback(text) {
        if (!this.voiceFeedbackEnabled || !text) return;

        console.log('播放语音反馈:', text);

        try {
            // 显示语音状态
            this.showVoiceStatus(text);

            // 调用TTS API
            const response = await fetch('/api/tts/speak', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    text: text,
                    voice: 'zh-CN-XiaoxiaoNeural',
                    rate: 'medium'
                })
            });

            if (response.ok) {
                const audioBlob = await response.blob();
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = new Audio(audioUrl);

                audio.onended = () => {
                    this.hideVoiceStatus();
                    URL.revokeObjectURL(audioUrl);
                };

                audio.onerror = () => {
                    console.error('音频播放失败');
                    this.hideVoiceStatus();
                };

                await audio.play();
            } else {
                console.error('TTS请求失败');
                this.hideVoiceStatus();
            }
        } catch (error) {
            console.error('语音反馈失败:', error);
            this.hideVoiceStatus();
        }
    }

    showVoiceStatus(text) {
        const statusEl = document.getElementById('voiceFeedbackStatus');
        const textEl = document.getElementById('currentVoiceText');
        const ttsIndicator = document.getElementById('ttsIndicator');
        const ttsCurrentText = document.getElementById('ttsCurrentText');

        if (statusEl && textEl) {
            textEl.textContent = text;
            statusEl.style.display = 'block';
        }

        if (ttsIndicator && ttsCurrentText) {
            ttsCurrentText.textContent = text;
            ttsIndicator.style.display = 'block';
        }
    }

    hideVoiceStatus() {
        const statusEl = document.getElementById('voiceFeedbackStatus');
        const ttsIndicator = document.getElementById('ttsIndicator');

        if (statusEl) {
            statusEl.style.display = 'none';
        }

        if (ttsIndicator) {
            ttsIndicator.style.display = 'none';
        }
    }

    toggleVoiceFeedback() {
        this.voiceFeedbackEnabled = !this.voiceFeedbackEnabled;

        const toggleBtn = document.getElementById('ttsToggleAnalysis');
        if (toggleBtn) {
            const icon = toggleBtn.querySelector('i');
            if (this.voiceFeedbackEnabled) {
                icon.className = 'bi bi-volume-up';
                toggleBtn.classList.remove('btn-outline-secondary');
                toggleBtn.classList.add('btn-outline-primary');
            } else {
                icon.className = 'bi bi-volume-mute';
                toggleBtn.classList.remove('btn-outline-primary');
                toggleBtn.classList.add('btn-outline-secondary');
            }
        }

        console.log('语音反馈', this.voiceFeedbackEnabled ? '已开启' : '已关闭');
    }

    pauseVoiceFeedback() {
        // 暂停当前播放的语音
        console.log('暂停语音反馈');
        this.hideVoiceStatus();
    }

    stopVoiceFeedback() {
        // 停止当前播放的语音
        console.log('停止语音反馈');
        this.hideVoiceStatus();
    }

    // ==================== 控制方法 ====================

    updateProgressText(text) {
        const progressTextEl = document.getElementById('progressText');
        if (progressTextEl) {
            progressTextEl.textContent = text;
        }
    }

    showControlButton(buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.style.display = 'block';
        }
    }

    hideControlButton(buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.style.display = 'none';
        }
    }

    hideAllControlButtons() {
        this.hideControlButton('startPlanningBtn');
        this.hideControlButton('pausePlanningBtn');
        this.hideControlButton('cancelPlanningBtn');
    }

    showStartPlanningButton() {
        console.log('显示立即规划按钮');
        this.hideControlButton('cancelPlanningBtn');
        this.showControlButton('startPlanningBtn');

        // 额外的调试和强制显示
        const startBtn = document.getElementById('startPlanningBtn');
        console.log('立即规划按钮元素:', startBtn);
        if (startBtn) {
            startBtn.style.display = 'block';
            startBtn.style.visibility = 'visible';
            console.log('强制显示立即规划按钮');
        } else {
            console.error('找不到立即规划按钮元素');
        }
    }

    showWaitingView() {
        // 显示等待状态 - 重置分析步骤为占位符状态
        const analysisSteps = document.getElementById('analysisSteps');
        if (analysisSteps) {
            analysisSteps.innerHTML = `
                <div class="analysis-placeholder">
                    <div class="text-center text-muted">
                        <i class="bi bi-clock-history"></i>
                        <p class="mt-2">等待开始分析...</p>
                        <small>AI将为您透明化展示整个分析思考过程</small>
                    </div>
                </div>
            `;
        }
    }

    showAnalysisView() {
        // 显示分析视图 - 清除占位符，准备显示分析步骤
        const analysisSteps = document.getElementById('analysisSteps');
        if (analysisSteps) {
            // 移除占位符，准备显示实际的分析步骤
            const placeholder = analysisSteps.querySelector('.analysis-placeholder');
            if (placeholder) {
                placeholder.remove();
            }
        }
        console.log('分析视图已激活，准备显示分析步骤');
    }

    showPlanningView() {
        console.log('切换到规划视图');

        // 确保右侧行程视图显示
        const itineraryView = document.getElementById('itineraryView');
        const routeView = document.getElementById('routeView');

        if (itineraryView) {
            itineraryView.style.display = 'block';
            // 清空等待内容，准备显示实际行程
            itineraryView.innerHTML = `
                <div class="text-center text-muted">
                    <i class="bi bi-gear-fill spin"></i>
                    <p class="mt-2">AI正在生成行程...</p>
                    <small>请稍候，智能规划进行中</small>
                </div>
            `;
        }

        if (routeView) {
            routeView.style.display = 'none';
        }

        // 更新右侧内容为规划进度
        this.updateRightPanelForPlanning();
    }

    updateRightPanelForPlanning() {
        // 更新右侧面板显示规划进度信息
        const rightPanel = document.querySelector('.itinerary-panel .panel-body');
        if (rightPanel) {
            rightPanel.innerHTML = `
                <div class="planning-progress">
                    <div class="text-center mb-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">规划中...</span>
                        </div>
                        <h4 class="mt-3">🚗 开始智能规划</h4>
                        <p class="text-muted">AI正在为您生成个性化的自驾行程...</p>
                    </div>

                    <div class="planning-steps">
                        <div class="planning-step">
                            <div class="step-icon">
                                <i class="bi bi-search text-primary"></i>
                            </div>
                            <div class="step-content">
                                <h6>🔍 搜索优质POI</h6>
                                <p class="text-muted">正在搜索符合您偏好的景点、餐厅和酒店...</p>
                            </div>
                        </div>

                        <div class="planning-step">
                            <div class="step-icon">
                                <i class="bi bi-geo-alt text-warning"></i>
                            </div>
                            <div class="step-content">
                                <h6>🗺️ 优化行程路线</h6>
                                <p class="text-muted">基于自驾场景优化路线和时间安排...</p>
                            </div>
                        </div>

                        <div class="planning-step">
                            <div class="step-icon">
                                <i class="bi bi-battery-charging text-success"></i>
                            </div>
                            <div class="step-content">
                                <h6>⚡ 规划充电路线</h6>
                                <p class="text-muted">为您的${this.vehicleInfo.model || '爱车'}规划最佳充电站...</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    showItinerary(itinerary) {
        console.log('显示行程:', itinerary);

        // 确保行程视图已初始化
        this.showItineraryView();

        // 更新行程显示
        this.updateItineraryDisplay(itinerary);
    }

    updateItineraryDisplay(itinerary) {
        // 更新行程标题和描述
        const titleEl = document.getElementById('itineraryTitle');
        const descEl = document.getElementById('itineraryDescription');

        if (titleEl && itinerary.title) {
            titleEl.textContent = itinerary.title;
        }

        if (descEl && itinerary.description) {
            descEl.textContent = itinerary.description;
        }

        // 更新统计信息
        this.updateItineraryStats(itinerary);

        // 渲染每日行程
        this.renderDailyItinerary(itinerary);
    }

    updateItineraryStats(itinerary) {
        // 更新自驾相关的统计信息
        const totalDaysEl = document.getElementById('totalDays');
        const totalPOIsEl = document.getElementById('totalPOIs');
        const totalDistanceEl = document.getElementById('totalDistance');
        const chargingStopsEl = document.getElementById('chargingStops');
        const parkingSpotsEl = document.getElementById('parkingSpots');
        const estimatedBudgetEl = document.getElementById('estimatedBudget');

        // 从行程数据中提取统计信息
        const stats = this.extractItineraryStats(itinerary);

        if (totalDaysEl) totalDaysEl.textContent = stats.totalDays;
        if (totalPOIsEl) totalPOIsEl.textContent = stats.totalPOIs;
        if (totalDistanceEl) totalDistanceEl.textContent = `${stats.totalDistance}km`;
        if (chargingStopsEl) chargingStopsEl.textContent = stats.chargingStops;
        if (parkingSpotsEl) parkingSpotsEl.textContent = stats.parkingSpots;
        if (estimatedBudgetEl) estimatedBudgetEl.textContent = `¥${stats.estimatedBudget}`;
    }

    extractItineraryStats(itinerary) {
        // 从行程数据中提取统计信息
        let totalDays = 0;
        let totalPOIs = 0;
        let totalDistance = 0;
        let chargingStops = 0;
        let parkingSpots = 0;
        let estimatedBudget = 0;

        if (itinerary.daily_plans) {
            totalDays = Object.keys(itinerary.daily_plans).length;
            Object.values(itinerary.daily_plans).forEach(dayPlan => {
                if (Array.isArray(dayPlan)) {
                    totalPOIs += dayPlan.length;
                }
            });
        }

        if (itinerary.route_info) {
            totalDistance = itinerary.route_info.total_distance || 0;
            chargingStops = itinerary.route_info.charging_stops?.length || 0;
        }

        if (itinerary.parking_info) {
            parkingSpots = itinerary.parking_info.total_spots || 0;
        }

        if (itinerary.budget_info) {
            estimatedBudget = itinerary.budget_info.total_budget || 0;
        }

        return {
            totalDays,
            totalPOIs,
            totalDistance,
            chargingStops,
            parkingSpots,
            estimatedBudget
        };
    }

    renderDailyItinerary(itinerary) {
        // 渲染详细的每日行程
        console.log('渲染每日行程:', itinerary.daily_plans);

        const dailyItineraryEl = document.getElementById('dailyItinerary');
        if (!dailyItineraryEl) {
            console.warn('找不到dailyItinerary元素');
            return;
        }

        // 清空现有内容
        dailyItineraryEl.innerHTML = '';

        if (!itinerary.daily_plans || Object.keys(itinerary.daily_plans).length === 0) {
            dailyItineraryEl.innerHTML = '<div class="text-center text-muted">暂无行程安排</div>';
            return;
        }

        // 按天渲染行程
        Object.keys(itinerary.daily_plans).sort((a, b) => parseInt(a) - parseInt(b)).forEach(day => {
            const dayPlan = itinerary.daily_plans[day];
            if (!Array.isArray(dayPlan) || dayPlan.length === 0) return;

            const dayCard = document.createElement('div');
            dayCard.className = 'card mb-3';
            dayCard.innerHTML = `
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar-day"></i> 第${day}天行程
                        <small class="text-muted">(${dayPlan.length}个活动)</small>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        ${dayPlan.map((activity, index) => this.renderActivity(activity, index)).join('')}
                    </div>
                </div>
            `;

            dailyItineraryEl.appendChild(dayCard);
        });
    }

    renderActivity(activity, index) {
        const activityType = activity.type || 'attraction';
        const iconClass = activityType === 'dining' ? 'bi-cup-hot' : 'bi-geo-alt';
        const typeLabel = activityType === 'dining' ? '餐饮' : '景点';
        const typeClass = activityType === 'dining' ? 'badge-warning' : 'badge-primary';

        return `
            <div class="timeline-item">
                <div class="timeline-marker">
                    <i class="bi ${iconClass}"></i>
                </div>
                <div class="timeline-content">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">
                                ${activity.name || '未知活动'}
                                <span class="badge ${typeClass} ms-2">${typeLabel}</span>
                            </h6>
                            ${activity.address ? `<p class="text-muted small mb-1"><i class="bi bi-geo"></i> ${activity.address}</p>` : ''}
                            ${activity.description ? `<p class="mb-1">${activity.description}</p>` : ''}
                            ${activity.rating ? `<div class="mb-1"><i class="bi bi-star-fill text-warning"></i> ${activity.rating}</div>` : ''}
                        </div>
                        <div class="text-end">
                            ${activity.start_time ? `<div class="time-badge">${activity.start_time}</div>` : ''}
                            ${activity.end_time ? `<div class="time-badge text-muted">- ${activity.end_time}</div>` : ''}
                            ${activity.duration ? `<div class="small text-muted">${activity.duration}</div>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    pausePlanning() {
        console.log('暂停规划');
        // 实现暂停功能
    }

    cancelPlanning() {
        console.log('取消规划');

        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        this.isPlanning = false;
        this.resetUI();
    }

    switchView(viewType) {
        console.log('切换视图:', viewType);

        // 隐藏所有视图
        const listView = document.getElementById('listView');
        const mapView = document.getElementById('mapView');
        const routeView = document.getElementById('routeView');

        if (listView) listView.style.display = 'none';
        if (mapView) mapView.style.display = 'none';
        if (routeView) routeView.style.display = 'none';

        // 显示选中的视图
        const targetView = document.getElementById(`${viewType}View`);
        if (targetView) {
            targetView.style.display = 'block';
        }

        // 更新按钮状态
        this.updateViewButtons(viewType);
    }

    updateViewButtons(activeView) {
        const buttons = ['viewModeList', 'viewModeMap', 'viewModeRoute'];

        buttons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.classList.remove('btn-primary');
                button.classList.add('btn-outline-primary');
            }
        });

        const activeButton = document.getElementById(`viewMode${activeView.charAt(0).toUpperCase() + activeView.slice(1)}`);
        if (activeButton) {
            activeButton.classList.remove('btn-outline-primary');
            activeButton.classList.add('btn-primary');
        }
    }

    showError(message) {
        console.error('显示错误:', message);
        alert(message); // 简单的错误显示，可以后续优化为更好的UI
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.autoPilotApp = new AutoPilotAppV3Refactored();
});
