"""
LangGraph 节点函数实现 (V3.0 - 统一架构版)

每个节点函数负责执行特定的任务步骤，并更新状态。
支持两阶段工作流：意图分析 + ICP迭代规划
"""
import asyncio
import json
import logging
import re
from typing import Dict, Any, List
from datetime import datetime
from .state import StandardAgentState, TravelPlanState
from src.tools.unified_registry import unified_registry
from src.core.logger import get_logger

logger = get_logger("travel_planner_nodes")


def _extract_basic_info_from_query(user_query: str) -> Dict[str, Any]:
    """
    从用户查询中提取基本旅行信息

    当LLM调用失败时，作为后备方案使用简单的文本解析
    """
    if not user_query:
        return {
            "destinations": ["未知目的地"],
            "travel_days": 1,
            "travel_theme": ["休闲"],
            "group_size": 1,
            "departure_city": "",
            "is_multi_city": False
        }

    # 提取目的地信息
    destinations = []
    departure_city = ""

    # 匹配"从A到B"、"A去B"、"A到B"等模式
    route_patterns = [
        r'从(.+?)到(.+?)(?:玩|游|旅游|旅行)',
        r'从(.+?)去(.+?)(?:玩|游|旅游|旅行)',
        r'(.+?)到(.+?)(?:玩|游|旅游|旅行)',
        r'(.+?)去(.+?)(?:玩|游|旅游|旅行)',
        r'在(.+?)(?:玩|游|旅游|旅行)',
        r'去(.+?)(?:玩|游|旅游|旅行)'
    ]

    for pattern in route_patterns:
        match = re.search(pattern, user_query)
        if match:
            if len(match.groups()) == 2:
                # 有出发地和目的地
                departure_city = match.group(1).strip()
                destinations.append(match.group(2).strip())
                break
            elif len(match.groups()) == 1:
                # 只有目的地
                destinations.append(match.group(1).strip())
                break

    # 如果没有匹配到，尝试提取城市名
    if not destinations:
        # 常见城市名模式
        city_pattern = r'(北京|上海|广州|深圳|杭州|南京|苏州|成都|重庆|西安|武汉|长沙|郑州|济南|青岛|大连|沈阳|哈尔滨|长春|石家庄|太原|呼和浩特|银川|西宁|乌鲁木齐|拉萨|昆明|贵阳|南宁|海口|三亚|福州|厦门|南昌|合肥|兰州|天津|宁波|温州|无锡|常州|徐州|扬州|泰州|盐城|淮安|连云港|宿迁|镇江|莆田|泉州|漳州|龙岩|三明|南平|宁德)'
        cities = re.findall(city_pattern, user_query)
        if cities:
            if len(cities) >= 2:
                departure_city = cities[0]
                destinations = cities[1:]
            else:
                destinations = cities

    # 提取天数信息
    travel_days = 1
    day_patterns = [
        r'(\d+)天',
        r'(\d+)日',
        r'玩(\d+)天',
        r'游(\d+)天',
        r'(\d+)天(\d+)夜',
        r'(\d+)夜(\d+)天',
        r'(一|二|三|四|五|六|七|八|九|十)天',
        r'(两|俩)天',
        r'(半)天'
    ]

    # 中文数字映射
    chinese_numbers = {
        '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
        '两': 2, '俩': 2, '半': 0.5
    }

    for pattern in day_patterns:
        match = re.search(pattern, user_query)
        if match:
            day_str = match.group(1)
            if day_str.isdigit():
                travel_days = int(day_str)
            elif day_str in chinese_numbers:
                travel_days = chinese_numbers[day_str]
            break

    # 判断是否多城市
    is_multi_city = len(destinations) > 1

    # 如果没有提取到目的地，使用默认值
    if not destinations:
        destinations = ["未知目的地"]

    return {
        "destinations": destinations,
        "travel_days": travel_days,
        "travel_theme": ["休闲"],
        "group_size": 1,
        "departure_city": departure_city,
        "is_multi_city": is_multi_city
    }


def _fix_json_format(json_content: str) -> str:
    """
    修复常见的JSON格式问题
    """
    import re

    # 开始修复过程
    fixed = json_content.strip()

    # 1. 修复单引号为双引号（但要小心不要破坏字符串内容）
    # 只替换属性名的单引号
    fixed = re.sub(r"'(\w+)':", r'"\1":', fixed)
    # 替换字符串值的单引号
    fixed = re.sub(r":\s*'([^']*)'", r': "\1"', fixed)

    # 2. 修复尾随逗号
    fixed = re.sub(r',\s*}', '}', fixed)
    fixed = re.sub(r',\s*]', ']', fixed)

    # 3. 修复缺少引号的属性名（但不要影响已经有引号的）
    fixed = re.sub(r'([{,]\s*)(\w+)(\s*):', r'\1"\2"\3:', fixed)

    # 4. 修复多余的逗号
    fixed = re.sub(r',\s*,', ',', fixed)

    # 5. 修复缺少逗号的情况
    fixed = re.sub(r'}\s*{', '},{', fixed)
    fixed = re.sub(r']\s*[', '],[', fixed)

    # 6. 修复布尔值和null值的大小写
    fixed = re.sub(r'\bTrue\b', 'true', fixed)
    fixed = re.sub(r'\bFalse\b', 'false', fixed)
    fixed = re.sub(r'\bNone\b', 'null', fixed)

    # 7. 移除注释（如果有的话）
    fixed = re.sub(r'//.*$', '', fixed, flags=re.MULTILINE)
    fixed = re.sub(r'/\*.*?\*/', '', fixed, flags=re.DOTALL)

    return fixed


def analyze_core_intent(state: TravelPlanState) -> Dict[str, Any]:
    """分析核心意图节点
    
    解析用户的原始查询，提取目的地、时间、偏好等核心信息。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析核心意图")
    
    # TODO: 调用分析服务，获得结构化数据
    # core_intent = analysis_service.analyze_core_intent(state['original_query'])
    
    # 临时实现 - 简单解析
    query = state.get('original_query', '')
    destinations = []
    
    # 简单的目的地提取逻辑
    if '上海' in query:
        destinations.append('上海')
    if '北京' in query:
        destinations.append('北京')
    if '杭州' in query:
        destinations.append('杭州')
    
    if not destinations:
        destinations = ['上海']  # 默认目的地
    
    core_intent = {
        'destinations': destinations,
        'duration_days': 3,  # 默认3天
        'travel_type': 'leisure',
        'budget_range': 'medium'
    }
    
    # 生成旁白文本
    narration_text = f"我理解您想要规划一个{len(destinations)}个城市的旅行，主要目的地包括：{', '.join(destinations)}"
    
    return {
        "destinations": destinations,
        "core_intent": core_intent,
        "current_narration_text": narration_text,
        "current_step": "核心意图分析完成",
        "progress_percentage": 20
    }


def analyze_multi_city_strategy(state: TravelPlanState) -> Dict[str, Any]:
    """分析多城市策略节点
    
    如果识别出多个目的地，则生成宏观策略。
    在交互模式下，设置clarification_needed标志以暂停并等待用户确认。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析多城市策略")
    
    destinations = state.get("destinations", [])
    
    if len(destinations) > 1:
        # 生成多城市策略
        total_days = state.get("core_intent", {}).get("duration_days", 3)
        days_per_city = max(1, total_days // len(destinations))
        
        strategy = {
            "order": destinations,
            "split": [
                {"city": city, "days": days_per_city} 
                for city in destinations
            ]
        }
        
        narration_text = f"建议您按照 {' -> '.join(destinations)} 的顺序游览，每个城市安排{days_per_city}天"
        
        # 根据模式决定是否需要澄清
        needs_clarification = state.get("execution_mode") == "interactive"
        
        return {
            "multi_city_strategy": strategy,
            "current_narration_text": narration_text,
            "clarification_needed": "multi_city_strategy" if needs_clarification else None,
            "current_step": "多城市策略分析完成",
            "progress_percentage": 30
        }
    
    # 单目的地，直接跳过
    return {
        "current_step": "单目的地，跳过多城市策略",
        "progress_percentage": 30
    }


def analyze_driving_context(state: TravelPlanState) -> Dict[str, Any]:
    """分析驾驶情境节点
    
    分析用户的车辆信息和驾驶需求。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析驾驶情境")
    
    # TODO: 调用用户画像服务获取车辆信息
    # vehicle_info = user_profile_service.get_vehicle_info(state['user_id'])
    
    # 临时实现
    vehicle_info = {
        "model": "Tesla Model Y",
        "nominal_range_km": 450,
        "charge_type": "fast"
    }
    
    # 设定驾驶策略
    if vehicle_info and vehicle_info.get("nominal_range_km"):
        driving_strategy = "range_aware"
        planning_range_km = vehicle_info["nominal_range_km"] * 0.8  # 保守系数
        narration_text = f"检测到您的{vehicle_info['model']}，我会按照续航的80%为您规划路线，确保行程安全"
    else:
        driving_strategy = "general_assistance"
        planning_range_km = None
        narration_text = "我会为您的自驾行程提供停车场和充电站信息"
    
    return {
        "user_vehicle_info": vehicle_info,
        "driving_strategy": driving_strategy,
        "planning_range_km": planning_range_km,
        "range_buffer_factor": 0.8,
        "current_narration_text": narration_text,
        "current_step": "驾驶情境分析完成",
        "progress_percentage": 40
    }


def analyze_preferences(state: TravelPlanState) -> Dict[str, Any]:
    """分析用户偏好节点
    
    整合分析景点、美食、住宿等偏好。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析用户偏好")
    
    # TODO: 调用分析服务
    # attraction_prefs = analysis_service.analyze_attraction_preferences(...)
    # food_prefs = analysis_service.analyze_food_preferences(...)
    # accommodation_prefs = analysis_service.analyze_accommodation_preferences(...)
    
    # 临时实现
    attraction_prefs = {
        "types": ["文化古迹", "自然风光"],
        "crowd_preference": "适中",
        "activity_level": "轻松"
    }
    
    food_prefs = {
        "cuisine_types": ["本地特色", "川菜"],
        "price_range": "中等",
        "dietary_restrictions": []
    }
    
    accommodation_prefs = {
        "type": "酒店",
        "star_rating": "4星",
        "location_preference": "市中心"
    }
    
    narration_text = "根据您的偏好，我会为您推荐文化古迹和自然风光，安排本地特色美食，选择市中心的4星酒店"
    
    return {
        "attraction_preferences": attraction_prefs,
        "food_preferences": food_prefs,
        "accommodation_preferences": accommodation_prefs,
        "current_narration_text": narration_text,
        "current_step": "用户偏好分析完成",
        "progress_percentage": 50
    }


def wait_for_user_input(state: TravelPlanState) -> Dict[str, Any]:
    """等待用户输入节点
    
    在交互模式下暂停执行，等待用户确认。
    """
    logger.info(f"[{state.get('trace_id')}] 等待用户输入")
    
    # 这个节点主要是标记状态，实际的等待逻辑在图的执行层处理
    return {
        "current_step": "等待用户确认",
        "progress_percentage": state.get("progress_percentage", 50)
    }


def route_after_analysis(state: TravelPlanState) -> str:
    """分析后的路由函数
    
    决定下一步走向：是否需要等待用户输入。
    """
    if state.get("clarification_needed"):
        return "wait_for_user_input"
    else:
        return "continue_analysis"


def decide_planning_or_end(state: TravelPlanState) -> str:
    """决定是继续规划还是结束

    在分析的最后阶段决定是继续规划还是结束。
    """
    # 在自动模式下，总是继续
    if state.get("execution_mode") == "automatic":
        return "execute_planning_stage"

    # 在交互模式下，根据用户反馈决定
    if state.get("user_feedback") == "proceed":
        return "execute_planning_stage"
    else:
        return "__end__"


# ==================== V3.0 新增节点：ICP迭代规划循环 ====================

async def planner_agent_node(state: StandardAgentState) -> Dict[str, Any]:
    """
    规划器Agent节点 - ICP循环的"思考"部分

    基于当前状态和上下文，决定下一步最合理的操作
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] Planner Agent开始思考")

    try:
        # 获取规划工具
        think_tool = unified_registry.get_planner_tool("generate_planning_thought")
        action_tool = unified_registry.get_planner_tool("select_next_action")
        check_tool = unified_registry.get_planner_tool("check_planning_completion")

        if not all([think_tool, action_tool, check_tool]):
            raise ValueError("Required planner tools not found")

        # 获取当前规划状态
        icp_context = state.get("icp_context", {})
        current_planning_state = {
            "daily_plans": state.get("daily_plans", {}),
            "daily_time_tracker": state.get("daily_time_tracker", {}),
            "total_budget_tracker": state.get("total_budget_tracker", 0.0),
            "tool_results": state.get("tool_results", {}),
            "planning_log": state.get("planning_log", []),
            "consolidated_intent": state.get("consolidated_intent", {}),
            "accommodation_planned": state.get("accommodation_planned", False)
        }

        step_number = len(current_planning_state.get("planning_log", [])) + 1

        # 步骤1: 思考
        thought_result = think_tool(
            current_planning_state,
            icp_context,
            step_number
        )

        # 发布思考日志
        if event_bus:
            await event_bus.notify_planning_log(
                task_id,
                thought_result.get("thought_content", ""),
                step_number
            )

        # 步骤2: 选择行动
        available_tools = icp_context.get("available_tools", ["search_poi"])
        action_result = action_tool(
            thought_result,
            available_tools,
            current_planning_state
        )

        # 步骤3: 检查是否完成
        completion_check = check_tool(current_planning_state, icp_context)

        # 决定下一步行动
        selected_action = action_result.get("selected_action", {})
        tool_name = selected_action.get("tool_name", "")

        # 如果规划完成，返回finish指令
        if completion_check.get("is_complete", False):
            return {
                "current_action": {"tool_name": "finish_planning", "parameters": {}},
                "thought_result": thought_result,
                "action_result": action_result,
                "completion_check": completion_check,
                "planning_finished": True
            }

        # 否则返回下一步行动
        return {
            "current_action": selected_action,
            "thought_result": thought_result,
            "action_result": action_result,
            "completion_check": completion_check,
            "planning_finished": False
        }

    except Exception as e:
        logger.error(f"[{task_id}] Planner Agent思考失败: {str(e)}")
        return {
            "has_error": True,
            "error_message": f"Planner Agent思考失败: {str(e)}",
            "current_action": {"tool_name": "finish_planning", "parameters": {}},
            "planning_finished": True
        }


async def tool_executor_node(state: StandardAgentState) -> Dict[str, Any]:
    """
    工具执行器节点 - ICP循环的"行动"部分

    执行Planner Agent决定的动作，并观察结果
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] Tool Executor开始执行行动")

    try:
        # 获取观察和更新工具
        observe_tool = unified_registry.get_planner_tool("observe_action_result")
        update_tool = unified_registry.get_planner_tool("update_planning_state")

        if not all([observe_tool, update_tool]):
            raise ValueError("Required observation tools not found")

        # 获取当前行动
        current_action = state.get("current_action", {})
        tool_name = current_action.get("tool_name", "")
        tool_params = current_action.get("parameters", {})

        if not tool_name:
            raise ValueError("No action specified")

        # 获取当前规划状态
        current_planning_state = {
            "daily_plans": state.get("daily_plans", {}),
            "daily_time_tracker": state.get("daily_time_tracker", {}),
            "total_budget_tracker": state.get("total_budget_tracker", 0.0),
            "tool_results": state.get("tool_results", {}),
            "planning_log": state.get("planning_log", []),
            "consolidated_intent": state.get("consolidated_intent", {}),
            "accommodation_planned": state.get("accommodation_planned", False)
        }

        # 执行行动
        action_execution_result = None
        if tool_name in ["search_poi", "geocode", "get_driving_route"]:
            try:
                # 执行Action Tool
                action_execution_result = await unified_registry.execute_action_tool(
                    tool_name,
                    task_id=task_id,
                    **tool_params
                )
            except Exception as e:
                logger.warning(f"[{task_id}] 工具执行失败: {str(e)}")
                action_execution_result = None
        else:
            # 模拟其他工具执行
            action_execution_result = {"status": "simulated", "data": []}

        # 观察结果
        observation = observe_tool(
            current_action,
            action_execution_result,
            current_planning_state
        )

        # 更新状态
        updated_planning_state = update_tool(
            current_planning_state,
            current_action,
            action_execution_result,
            observation
        )

        # 发布行程更新事件
        if observation.get("success", False) and "target_day" in current_action:
            target_day = current_action["target_day"]
            daily_plans = updated_planning_state.get("daily_plans", {})
            if target_day in daily_plans and event_bus:
                activities = daily_plans[target_day]
                for activity in activities:
                    await event_bus.notify_itinerary_update(
                        task_id,
                        target_day,
                        activity
                    )

        return {
            "daily_plans": updated_planning_state.get("daily_plans", {}),
            "daily_time_tracker": updated_planning_state.get("daily_time_tracker", {}),
            "total_budget_tracker": updated_planning_state.get("total_budget_tracker", 0.0),
            "tool_results": updated_planning_state.get("tool_results", {}),
            "planning_log": updated_planning_state.get("planning_log", []),
            "accommodation_planned": updated_planning_state.get("accommodation_planned", False),
            "last_observation": observation,
            "last_action_result": action_execution_result
        }

    except Exception as e:
        logger.error(f"[{task_id}] Tool Executor执行失败: {str(e)}")
        return {
            "has_error": True,
            "error_message": f"Tool Executor执行失败: {str(e)}",
            "last_observation": {"success": False, "observation": str(e)}
        }


def route_icp_action(state: StandardAgentState) -> str:
    """
    ICP循环的路由函数

    根据Planner Agent的决定，路由到工具执行器或结束规划
    """
    current_action = state.get("current_action", {})
    tool_name = current_action.get("tool_name", "")
    planning_finished = state.get("planning_finished", False)

    if planning_finished or tool_name == "finish_planning":
        return "END"
    else:
        return "tool_executor"


# ==================== V3.0 新增节点：两阶段意图分析 ====================

async def run_framework_analysis(state: StandardAgentState) -> Dict[str, Any]:
    """
    执行核心框架分析 (V3.0)

    分析用户的旅行核心需求，包括目的地、天数、主题、多城市策略和自驾情境
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] 开始核心框架分析")

    # 发布阶段开始事件
    if event_bus:
        await event_bus.notify_phase_start(
            task_id,
            "framework_analysis",
            "核心框架分析",
            "正在分析您的旅行核心需求和框架..."
        )

    try:
        # 获取Planner Tool
        format_prompt = unified_registry.get_planner_tool("format_framework_analysis_prompt")
        if not format_prompt:
            raise ValueError("format_framework_analysis_prompt tool not found")

        # 准备输入数据
        user_query = ""
        messages = state.get("messages", [])
        if messages:
            # 处理LangGraph消息格式
            last_message = messages[-1]
            if hasattr(last_message, 'content'):
                user_query = last_message.content
            elif isinstance(last_message, dict):
                user_query = last_message.get("content", "")
            else:
                user_query = str(last_message)

        user_profile = state.get("user_profile", {})
        current_time = datetime.now().isoformat()

        # 格式化提示词
        prompt = format_prompt(user_query, user_profile, current_time)

        # 调用LLM进行框架分析
        from src.agents.services.reasoning_service import ReasoningService
        reasoning_service = ReasoningService(llm_role="basic")

        # 直接使用consolidated prompt，它已经包含了完整的系统指令
        messages = [
            {"role": "user", "content": prompt}
        ]

        # 调用LLM获取分析结果（带重试机制）
        framework_analysis = None
        max_retries = 3

        for attempt in range(max_retries):
            try:
                logger.info(f"[{task_id}] 开始调用真实LLM进行框架分析... (尝试 {attempt + 1}/{max_retries})")

                llm_response = await reasoning_service.simple_chat(
                    messages=messages,
                    max_tokens=2000
                )

                logger.info(f"[{task_id}] LLM响应长度: {len(llm_response)}")
                logger.info(f"[{task_id}] LLM原始响应内容: {repr(llm_response)}")

                # 尝试解析LLM响应为结构化数据
                import json
                import re

                # 提取JSON部分
                json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
                if json_match:
                    json_content = json_match.group()
                    logger.info(f"[{task_id}] 提取的JSON内容: {repr(json_content)}")

                    # 尝试使用更robust的JSON解析
                    try:
                        framework_analysis = json.loads(json_content)
                        logger.info(f"[{task_id}] LLM框架分析成功，目的地: {framework_analysis.get('core_intent', {}).get('destinations', [])}")
                        break  # 成功解析，跳出重试循环
                    except json.JSONDecodeError as json_error:
                        logger.error(f"[{task_id}] JSON解析失败: {str(json_error)}")
                        logger.error(f"[{task_id}] 错误位置: line {json_error.lineno}, column {json_error.colno}")
                        logger.error(f"[{task_id}] 错误附近的内容: {json_content[max(0, json_error.pos-50):json_error.pos+50]}")

                        # 尝试修复常见的JSON格式问题
                        fixed_json = _fix_json_format(json_content)
                        if fixed_json != json_content:
                            logger.info(f"[{task_id}] 尝试修复后的JSON: {repr(fixed_json)}")
                            framework_analysis = json.loads(fixed_json)
                            logger.info(f"[{task_id}] JSON修复成功")
                            break  # 修复成功，跳出重试循环
                        else:
                            if attempt < max_retries - 1:
                                logger.warning(f"[{task_id}] JSON解析失败，将进行第 {attempt + 2} 次重试...")
                                await asyncio.sleep(1)  # 等待1秒后重试
                                continue
                            else:
                                raise json_error
                else:
                    if attempt < max_retries - 1:
                        logger.warning(f"[{task_id}] 无法从LLM响应中提取JSON，将进行第 {attempt + 2} 次重试...")
                        await asyncio.sleep(1)  # 等待1秒后重试
                        continue
                    else:
                        raise ValueError("无法从LLM响应中提取JSON")

            except Exception as llm_error:
                if attempt < max_retries - 1:
                    logger.warning(f"[{task_id}] LLM调用失败，将进行第 {attempt + 2} 次重试: {str(llm_error)}")
                    await asyncio.sleep(2)  # 等待2秒后重试
                    continue
                else:
                    # 所有重试都失败了，抛出异常
                    logger.error(f"[{task_id}] LLM调用重试 {max_retries} 次后仍然失败: {str(llm_error)}")
                    raise Exception(f"框架分析失败：LLM调用重试 {max_retries} 次后仍然失败 - {str(llm_error)}")

        # 检查是否成功获取到分析结果
        if framework_analysis is None:
            raise Exception("框架分析失败：无法获取LLM分析结果")

        # 生成语音反馈文本（符合PRD文档A.1步骤要求）
        destinations_str = "、".join(framework_analysis.get("core_intent", {}).get("destinations", ["未知目的地"]))
        travel_days = framework_analysis.get("core_intent", {}).get("travel_days", 1)
        voice_text = f"收到！一个去往{destinations_str}的{travel_days}日自驾游，听起来真不错。让我先为您分析一下行程和车辆情况。"

        # 发布阶段结束事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "framework_analysis",
                "success",
                {
                    "analysis_result": framework_analysis,
                    "voice_text": voice_text,
                    "phase_code": "A.1",
                    "phase_name": "核心框架分析"
                }
            )

        logger.info(f"[{task_id}] 核心框架分析完成")

        return {
            "framework_analysis": framework_analysis,
            "current_phase": "framework_analysis_completed",
            "voice_text": voice_text
        }

    except Exception as e:
        logger.error(f"[{task_id}] 核心框架分析失败: {str(e)}")
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "framework_analysis")

        return {
            "has_error": True,
            "error_message": f"核心框架分析失败: {str(e)}",
            "current_phase": "error"
        }


async def run_preference_analysis(state: StandardAgentState) -> Dict[str, Any]:
    """
    执行个性化偏好分析 (V3.0)

    基于核心框架分析结果，深入分析用户的景点、美食、住宿偏好
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] 开始个性化偏好分析")

    # 发布阶段开始事件
    if event_bus:
        await event_bus.notify_phase_start(
            task_id,
            "preference_analysis",
            "个性化偏好分析",
            "正在深入分析您的景点、美食、住宿偏好..."
        )

    try:
        # 获取Planner Tool
        format_prompt = unified_registry.get_planner_tool("format_preference_analysis_prompt")
        if not format_prompt:
            raise ValueError("format_preference_analysis_prompt tool not found")

        # 获取前一步的结果
        framework_result = state.get("framework_analysis", {})
        if not framework_result:
            raise ValueError("framework_analysis result not found")

        # 准备输入数据
        user_query = ""
        messages = state.get("messages", [])
        if messages:
            # 处理LangGraph消息格式
            last_message = messages[-1]
            if hasattr(last_message, 'content'):
                user_query = last_message.content
            elif isinstance(last_message, dict):
                user_query = last_message.get("content", "")
            else:
                user_query = str(last_message)

        user_profile = state.get("user_profile", {})
        current_time = datetime.now().isoformat()

        # 格式化提示词
        prompt = format_prompt(user_query, user_profile, framework_result, current_time)

        # 调用LLM进行偏好分析
        from src.agents.services.reasoning_service import ReasoningService
        reasoning_service = ReasoningService(llm_role="basic")

        # 直接使用consolidated prompt，它已经包含了完整的系统指令
        messages = [
            {"role": "user", "content": prompt}
        ]

        # 调用LLM获取偏好分析结果（带重试机制）
        preference_analysis = None
        max_retries = 3

        for attempt in range(max_retries):
            try:
                logger.info(f"[{task_id}] 开始调用真实LLM进行偏好分析... (尝试 {attempt + 1}/{max_retries})")

                llm_response = await reasoning_service.simple_chat(
                    messages=messages,
                    max_tokens=2000
                )

                logger.info(f"[{task_id}] LLM偏好分析响应长度: {len(llm_response)}")
                logger.info(f"[{task_id}] LLM偏好分析原始响应: {repr(llm_response)}")

                # 尝试解析LLM响应为结构化数据
                import json
                import re

                # 提取JSON部分
                json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
                if json_match:
                    json_content = json_match.group()
                    logger.info(f"[{task_id}] 提取的偏好分析JSON: {repr(json_content)}")

                    # 尝试使用更robust的JSON解析
                    try:
                        preference_analysis = json.loads(json_content)
                        logger.info(f"[{task_id}] LLM偏好分析成功")
                        break  # 成功解析，跳出重试循环
                    except json.JSONDecodeError as json_error:
                        logger.error(f"[{task_id}] 偏好分析JSON解析失败: {str(json_error)}")
                        logger.error(f"[{task_id}] 错误位置: line {json_error.lineno}, column {json_error.colno}")
                        logger.error(f"[{task_id}] 错误附近的内容: {json_content[max(0, json_error.pos-50):json_error.pos+50]}")

                        # 尝试修复常见的JSON格式问题
                        fixed_json = _fix_json_format(json_content)
                        if fixed_json != json_content:
                            logger.info(f"[{task_id}] 尝试修复后的偏好分析JSON: {repr(fixed_json)}")
                            preference_analysis = json.loads(fixed_json)
                            logger.info(f"[{task_id}] 偏好分析JSON修复成功")
                            break  # 修复成功，跳出重试循环
                        else:
                            if attempt < max_retries - 1:
                                logger.warning(f"[{task_id}] 偏好分析JSON解析失败，将进行第 {attempt + 2} 次重试...")
                                await asyncio.sleep(1)  # 等待1秒后重试
                                continue
                            else:
                                raise json_error
                else:
                    if attempt < max_retries - 1:
                        logger.warning(f"[{task_id}] 无法从LLM响应中提取JSON，将进行第 {attempt + 2} 次重试...")
                        await asyncio.sleep(1)  # 等待1秒后重试
                        continue
                    else:
                        raise ValueError("无法从LLM响应中提取JSON")

            except Exception as llm_error:
                if attempt < max_retries - 1:
                    logger.warning(f"[{task_id}] LLM调用失败，将进行第 {attempt + 2} 次重试: {str(llm_error)}")
                    await asyncio.sleep(2)  # 等待2秒后重试
                    continue
                else:
                    # 所有重试都失败了，抛出异常
                    logger.error(f"[{task_id}] LLM调用重试 {max_retries} 次后仍然失败: {str(llm_error)}")
                    raise Exception(f"偏好分析失败：LLM调用重试 {max_retries} 次后仍然失败 - {str(llm_error)}")

        # 检查是否成功获取到分析结果
        if preference_analysis is None:
            raise Exception("偏好分析失败：无法获取LLM分析结果")

        # 生成语音反馈文本（符合PRD文档A.2步骤要求）
        attraction_types = preference_analysis.get("attraction_preferences", {}).get("preferred_types", ["景点"])
        attraction_str = "和".join(attraction_types[:2])  # 取前两个类型
        voice_text = f"根据您的喜好，我发现您对{attraction_str}特别感兴趣。我会重点为您留意这类地方，并确保它们停车方便。"

        # 发布阶段结束事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "preference_analysis",
                "success",
                {
                    "analysis_result": preference_analysis,
                    "voice_text": voice_text,
                    "phase_code": "A.2",
                    "phase_name": "个性化偏好分析"
                }
            )

        logger.info(f"[{task_id}] 个性化偏好分析完成")

        return {
            "preference_analysis": preference_analysis,
            "current_phase": "preference_analysis_completed",
            "voice_text": voice_text
        }

    except Exception as e:
        logger.error(f"[{task_id}] 个性化偏好分析失败: {str(e)}")
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "preference_analysis")

        return {
            "has_error": True,
            "error_message": f"个性化偏好分析失败: {str(e)}",
            "current_phase": "error"
        }


async def prepare_planning_context(state: StandardAgentState) -> Dict[str, Any]:
    """
    准备ICP规划上下文 (V3.0)

    整合两步分析结果，为ICP迭代规划准备统一的上下文
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] 开始准备规划上下文")

    try:
        # 获取Planner Tools
        consolidate_tool = unified_registry.get_planner_tool("create_consolidated_intent")
        prepare_icp_tool = unified_registry.get_planner_tool("prepare_icp_context")
        extract_insights_tool = unified_registry.get_planner_tool("extract_key_insights")

        if not all([consolidate_tool, prepare_icp_tool, extract_insights_tool]):
            raise ValueError("Required planner tools not found")

        # 获取两步分析结果
        framework_analysis = state.get("framework_analysis", {})
        preference_analysis = state.get("preference_analysis", {})

        if not framework_analysis or not preference_analysis:
            raise ValueError("Framework or preference analysis results missing")

        # 整合意图
        consolidated_intent = consolidate_tool(framework_analysis, preference_analysis)

        # 提取关键洞察
        key_insights = extract_insights_tool(consolidated_intent)

        # 准备ICP上下文
        icp_context = prepare_icp_tool(consolidated_intent)

        # 生成语音反馈文本（符合PRD文档A.3步骤要求）
        voice_text = "好的，您的核心需求和个性化偏好我都清楚了，马上开始为您量身定制行程！"

        # 发布规划上下文准备完成事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "context_preparation",
                "success",
                {
                    "consolidated_intent": consolidated_intent,
                    "key_insights": key_insights,
                    "icp_context": icp_context,
                    "voice_text": voice_text,
                    "phase_code": "A.3",
                    "phase_name": "上下文整合",
                    "analysis_completed": True  # 标记分析阶段完成
                }
            )

        logger.info(f"[{task_id}] 规划上下文准备完成")

        return {
            "consolidated_intent": consolidated_intent,
            "key_insights": key_insights,
            "icp_context": icp_context,
            "current_phase": "planning_ready",
            "voice_text": voice_text,
            "analysis_completed": True
        }

    except Exception as e:
        logger.error(f"[{task_id}] 规划上下文准备失败: {str(e)}")
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "context_preparation")

        return {
            "has_error": True,
            "error_message": f"规划上下文准备失败: {str(e)}",
            "current_phase": "error"
        }


async def run_icp_planning(state: StandardAgentState) -> Dict[str, Any]:
    """
    执行ICP迭代式上下文规划 (V3.0)

    实现"思考-行动-观察"的迭代循环，直到规划完成
    """
    task_id = state.get("task_id")
    event_bus = state.get("notification_service")

    logger.info(f"[{task_id}] 开始ICP迭代规划")

    # 发布阶段开始事件
    if event_bus:
        await event_bus.notify_phase_start(
            task_id,
            "icp_planning",
            "ICP迭代规划",
            "开始智能迭代规划，为您制定详细行程..."
        )

    try:
        # 获取ICP工具
        think_tool = unified_registry.get_planner_tool("generate_planning_thought")
        action_tool = unified_registry.get_planner_tool("select_next_action")
        observe_tool = unified_registry.get_planner_tool("observe_action_result")
        update_tool = unified_registry.get_planner_tool("update_planning_state")
        check_tool = unified_registry.get_planner_tool("check_planning_completion")

        if not all([think_tool, action_tool, observe_tool, update_tool, check_tool]):
            raise ValueError("Required ICP tools not found")

        # 获取规划上下文
        icp_context = state.get("icp_context", {})
        consolidated_intent = state.get("consolidated_intent", {})

        if not icp_context:
            raise ValueError("ICP context not found")

        # 初始化规划状态
        current_planning_state = {
            "daily_plans": state.get("daily_plans", {}),
            "daily_time_tracker": state.get("daily_time_tracker", {}),
            "total_budget_tracker": state.get("total_budget_tracker", 0.0),
            "tool_results": state.get("tool_results", {}),
            "planning_log": state.get("planning_log", []),
            "consolidated_intent": consolidated_intent,
            "accommodation_planned": False
        }

        # ICP迭代循环
        max_iterations = 10
        step_number = 1

        for iteration in range(max_iterations):
            logger.info(f"[{task_id}] ICP迭代 {iteration + 1}/{max_iterations}")

            # 步骤1: 思考 (Think)
            thought_result = think_tool(
                current_planning_state,
                icp_context,
                step_number
            )

            # 发布思考日志
            if event_bus:
                await event_bus.notify_planning_log(
                    task_id,
                    thought_result.get("thought_content", ""),
                    step_number
                )

            # 步骤2: 选择行动
            available_tools = icp_context.get("available_tools", ["search_poi"])
            action_result = action_tool(
                thought_result,
                available_tools,
                current_planning_state
            )

            selected_action = action_result.get("selected_action", {})
            tool_name = selected_action.get("tool_name", "search_poi")
            tool_params = selected_action.get("parameters", {})

            # 步骤3: 执行行动
            try:
                if tool_name in ["search_poi", "geocode", "get_driving_route"]:
                    # 执行Action Tool
                    action_execution_result = await unified_registry.execute_action_tool(
                        tool_name,
                        task_id=task_id,
                        **tool_params
                    )
                else:
                    # 模拟其他工具执行
                    action_execution_result = {"status": "simulated", "data": []}

            except Exception as e:
                logger.warning(f"[{task_id}] 工具执行失败: {str(e)}")
                action_execution_result = None

            # 步骤4: 观察结果
            observation = observe_tool(
                selected_action,
                action_execution_result,
                current_planning_state
            )

            # 步骤5: 更新状态
            current_planning_state = update_tool(
                current_planning_state,
                selected_action,
                action_execution_result,
                observation
            )

            # 发布行程更新事件
            logger.info(f"[{task_id}] 检查行程更新条件: success={observation.get('success', False)}, target_day_in_action={'target_day' in selected_action}")
            if observation.get("success", False) and "target_day" in selected_action:
                target_day = selected_action["target_day"]
                daily_plans = current_planning_state.get("daily_plans", {})
                logger.info(f"[{task_id}] 准备发送行程更新: target_day={target_day}, daily_plans_keys={list(daily_plans.keys())}")
                if target_day in daily_plans:
                    activities = daily_plans[target_day]
                    logger.info(f"[{task_id}] 发送{len(activities)}个活动的行程更新事件")
                    for activity in activities:
                        if event_bus:
                            await event_bus.notify_itinerary_update(
                                task_id,
                                target_day,
                                activity
                            )
                            logger.info(f"[{task_id}] 已发送行程更新事件: day={target_day}, activity={activity.get('name', 'Unknown')}")
                else:
                    logger.warning(f"[{task_id}] target_day {target_day} 不在 daily_plans 中")
            else:
                logger.warning(f"[{task_id}] 行程更新条件不满足: observation={observation}, selected_action_keys={list(selected_action.keys())}")

            # 步骤6: 检查完成情况
            completion_check = check_tool(current_planning_state, icp_context)

            step_number += 1

            # 如果规划完成，退出循环
            if completion_check.get("is_complete", False):
                logger.info(f"[{task_id}] ICP规划完成，共执行{iteration + 1}次迭代")
                break

            # 如果质量太低，也可以选择退出
            if completion_check.get("quality_score", 0) < 0.3 and iteration > 5:
                logger.warning(f"[{task_id}] 规划质量较低，提前结束迭代")
                break

        # 生成最终结果
        final_itinerary = {
            "daily_plans": current_planning_state.get("daily_plans", {}),
            "planning_summary": {
                "total_iterations": step_number - 1,
                "completion_rate": completion_check.get("completion_rate", 0),
                "quality_score": completion_check.get("quality_score", 0),
                "planning_log": current_planning_state.get("planning_log", [])
            },
            "metadata": {
                "planning_method": "ICP",
                "generated_at": datetime.now().isoformat(),
                "consolidated_intent": current_planning_state.get("consolidated_intent", {})
            }
        }

        # 确保发送所有行程更新事件（防止前端错过）
        if event_bus:
            daily_plans = current_planning_state.get("daily_plans", {})
            logger.info(f"[{task_id}] 最终发送所有行程更新事件: {len(daily_plans)}天")
            for day, activities in daily_plans.items():
                logger.info(f"[{task_id}] 发送第{day}天的{len(activities)}个活动")
                for activity in activities:
                    await event_bus.notify_itinerary_update(
                        task_id,
                        day,
                        activity
                    )
                    logger.info(f"[{task_id}] 最终发送行程更新: day={day}, activity={activity.get('name', 'Unknown')}")

        # 发布阶段结束事件
        if event_bus:
            await event_bus.notify_phase_end(
                task_id,
                "icp_planning",
                "success",
                final_itinerary
            )

        logger.info(f"[{task_id}] ICP迭代规划完成")

        return {
            "daily_plans": current_planning_state.get("daily_plans", {}),
            "daily_time_tracker": current_planning_state.get("daily_time_tracker", {}),
            "total_budget_tracker": current_planning_state.get("total_budget_tracker", 0.0),
            "tool_results": current_planning_state.get("tool_results", {}),
            "planning_log": current_planning_state.get("planning_log", []),
            "final_itinerary": final_itinerary,
            "is_completed": True,
            "current_phase": "completed"
        }

    except Exception as e:
        logger.error(f"[{task_id}] ICP迭代规划失败: {str(e)}")
        if event_bus:
            await event_bus.notify_error(task_id, str(e), "icp_planning")

        return {
            "has_error": True,
            "error_message": f"ICP迭代规划失败: {str(e)}",
            "current_phase": "error"
        }
